"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1729849796381 = void 0;
class SchemaUpdate1729849796381 {
    constructor() {
        this.name = 'SchemaUpdate1729849796381';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."document_status_enum" AS ENUM('not_processed', 'in_extraction', 'data_extraction_finished', 'linking_data', 'linking_data_finished', 'error_processing')`);
        await queryRunner.query(`ALTER TABLE "document" ADD "status" "public"."document_status_enum" NOT NULL DEFAULT 'not_processed'`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."document_status_enum"`);
    }
}
exports.SchemaUpdate1729849796381 = SchemaUpdate1729849796381;
//# sourceMappingURL=1729849796381-schema-update.js.map