"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1738209468539 = void 0;
class SchemaUpdate1738209468539 {
    constructor() {
        this.name = 'SchemaUpdate1738209468539';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TYPE "public"."datapoint_request_status_enum" RENAME TO "datapoint_request_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."datapoint_request_status_enum" AS ENUM('not_answered', 'incomplete_data', 'no_data', 'complete_data', 'queued_for_generation')`);
        await queryRunner.query(`ALTER TABLE "datapoint_request" ALTER COLUMN "status" TYPE "public"."datapoint_request_status_enum" USING "status"::"text"::"public"."datapoint_request_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."datapoint_request_status_enum_old"`);
    }
    async down(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."datapoint_request_status_enum_old" AS ENUM('not_answered', 'incomplete_data', 'no_data', 'complete_data')`);
        await queryRunner.query(`ALTER TABLE "datapoint_request" ALTER COLUMN "status" TYPE "public"."datapoint_request_status_enum_old" USING "status"::"text"::"public"."datapoint_request_status_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."datapoint_request_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."datapoint_request_status_enum_old" RENAME TO "datapoint_request_status_enum"`);
    }
}
exports.SchemaUpdate1738209468539 = SchemaUpdate1738209468539;
//# sourceMappingURL=1738209468539-schema-update.js.map