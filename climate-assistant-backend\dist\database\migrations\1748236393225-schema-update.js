"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1748236393225 = void 0;
class SchemaUpdate1748236393225 {
    constructor() {
        this.name = 'SchemaUpdate1748236393225';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."data_request_status_enum_new" AS ENUM('incomplete_data', 'complete_data', 'not_reported')`);
        await queryRunner.query(`
      ALTER TABLE "data_request" 
      ALTER COLUMN status TYPE "public"."data_request_status_enum_new" 
      USING (
        CASE status::text
          WHEN 'not_answered' THEN 'not_reported'
          WHEN 'no_data' THEN 'incomplete_data'
          WHEN 'draft' THEN 'incomplete_data'
          WHEN 'approved_answer' THEN 'complete_data'
          WHEN 'incomplete_data' THEN 'incomplete_data'
          WHEN 'complete_data' THEN 'complete_data'
          ELSE 'incomplete_data'
        END
      )::"public"."data_request_status_enum_new"
    `);
        await queryRunner.query(`DROP TYPE "public"."data_request_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."data_request_status_enum_new" RENAME TO "data_request_status_enum"`);
    }
    async down(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."data_request_status_enum_old" AS ENUM('no_data', 'incomplete_data', 'draft', 'complete_data', 'approved_answer', 'not_answered')`);
        await queryRunner.query(`
      ALTER TABLE "data_request" 
      ALTER COLUMN status TYPE "public"."data_request_status_enum_old"
      USING (
        CASE status::text
          WHEN 'not_reported' THEN 'not_answered'
          WHEN 'incomplete_data' THEN 'incomplete_data'
          WHEN 'complete_data' THEN 'complete_data'
          ELSE 'not_answered'
        END
      )::"public"."data_request_status_enum_old"
    `);
        await queryRunner.query(`DROP TYPE "public"."data_request_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."data_request_status_enum_old" RENAME TO "data_request_status_enum"`);
    }
}
exports.SchemaUpdate1748236393225 = SchemaUpdate1748236393225;
//# sourceMappingURL=1748236393225-schema-update.js.map