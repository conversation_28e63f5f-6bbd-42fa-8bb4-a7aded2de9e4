"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1732210430177 = void 0;
class SchemaUpdate1732210430177 {
    constructor() {
        this.name = 'SchemaUpdate1732210430177';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "esrs_topic_datapoint" ("esrsTopicId" integer NOT NULL, "esrsDatapointId" integer NOT NULL, CONSTRAINT "PK_fc539be0fd52a02777385339c1d" PRIMARY KEY ("esrsTopicId", "esrsDatapointId"))`);
        await queryRunner.query(`ALTER TABLE "esrs_topic_datapoint" ADD CONSTRAINT "FK_c04b9f351d040532cb312e01973" FOREIGN KEY ("esrsTopicId") REFERENCES "esrs_topic"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "esrs_topic_datapoint" ADD CONSTRAINT "FK_7bad764f0b00ce815e9d0cd5541" FOREIGN KEY ("esrsDatapointId") REFERENCES "esrs_datapoint"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_topic_datapoint" DROP CONSTRAINT "FK_7bad764f0b00ce815e9d0cd5541"`);
        await queryRunner.query(`ALTER TABLE "esrs_topic_datapoint" DROP CONSTRAINT "FK_c04b9f351d040532cb312e01973"`);
        await queryRunner.query(`DROP TABLE "esrs_topic_datapoint"`);
    }
}
exports.SchemaUpdate1732210430177 = SchemaUpdate1732210430177;
//# sourceMappingURL=1732210430177-schema-update.js.map