"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1733416177418 = void 0;
class SchemaUpdate1733416177418 {
    constructor() {
        this.name = 'SchemaUpdate1733416177418';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_request" ADD "customUserRemark" text NOT NULL DEFAULT ''`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_request" DROP COLUMN "customUserRemark"`);
    }
}
exports.SchemaUpdate1733416177418 = SchemaUpdate1733416177418;
//# sourceMappingURL=1733416177418-schema-update.js.map