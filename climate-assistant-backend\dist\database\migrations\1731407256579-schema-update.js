"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1731407256579 = void 0;
class SchemaUpdate1731407256579 {
    constructor() {
        this.name = 'SchemaUpdate1731407256579';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "material_esrs_topic" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "document_chunk" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document_chunk" DROP COLUMN "createdAt"`);
        await queryRunner.query(`ALTER TABLE "material_esrs_topic" DROP COLUMN "createdAt"`);
    }
}
exports.SchemaUpdate1731407256579 = SchemaUpdate1731407256579;
//# sourceMappingURL=1731407256579-schema-update.js.map