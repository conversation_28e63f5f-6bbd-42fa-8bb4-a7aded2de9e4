"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ESRSTopicDisclosureRequirement = void 0;
const typeorm_1 = require("typeorm");
const esrs_topic_entity_1 = require("./esrs-topic.entity");
const esrs_disclosure_requirement_entity_1 = require("./esrs-disclosure-requirement.entity");
let ESRSTopicDisclosureRequirement = class ESRSTopicDisclosureRequirement {
};
exports.ESRSTopicDisclosureRequirement = ESRSTopicDisclosureRequirement;
__decorate([
    (0, typeorm_1.PrimaryColumn)(),
    __metadata("design:type", Number)
], ESRSTopicDisclosureRequirement.prototype, "esrsTopicId", void 0);
__decorate([
    (0, typeorm_1.PrimaryColumn)(),
    __metadata("design:type", Number)
], ESRSTopicDisclosureRequirement.prototype, "esrsDisclosureRequirementId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => esrs_topic_entity_1.ESRSTopic, (topic) => topic.disclosureRequirementRelations),
    (0, typeorm_1.JoinColumn)({ name: 'esrsTopicId' }),
    __metadata("design:type", esrs_topic_entity_1.ESRSTopic)
], ESRSTopicDisclosureRequirement.prototype, "topic", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => esrs_disclosure_requirement_entity_1.ESRSDisclosureRequirement, (dr) => dr.topicRelations),
    (0, typeorm_1.JoinColumn)({ name: 'esrsDisclosureRequirementId' }),
    __metadata("design:type", esrs_disclosure_requirement_entity_1.ESRSDisclosureRequirement)
], ESRSTopicDisclosureRequirement.prototype, "disclosureRequirement", void 0);
exports.ESRSTopicDisclosureRequirement = ESRSTopicDisclosureRequirement = __decorate([
    (0, typeorm_1.Entity)('esrs_topic_disclosure_requirement')
], ESRSTopicDisclosureRequirement);
//# sourceMappingURL=esrs_topic_disclosure_requirement.entity.js.map