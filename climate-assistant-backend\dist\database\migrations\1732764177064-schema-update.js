"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1732764177064 = void 0;
class SchemaUpdate1732764177064 {
    constructor() {
        this.name = 'SchemaUpdate1732764177064';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document" ADD "documentType" character varying`);
        await queryRunner.query(`ALTER TABLE "document" ADD "esrsCategory" text`);
        await queryRunner.query(`ALTER TABLE "document" ADD "year" integer`);
        await queryRunner.query(`ALTER TABLE "document" ADD "month" integer`);
        await queryRunner.query(`ALTER TABLE "document" ADD "day" integer`);
        await queryRunner.query(`ALTER TABLE "document" ADD "remarks" text`);
        await queryRunner.query(`ALTER TABLE "document" ADD "createdBy" uuid`);
        await queryRunner.query(`ALTER TABLE "document" ADD CONSTRAINT "FK_a581782d3fe36e6cb98e40b0572" FOREIGN KEY ("createdBy") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document" DROP CONSTRAINT "FK_a581782d3fe36e6cb98e40b0572"`);
        await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "createdBy"`);
        await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "remarks"`);
        await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "day"`);
        await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "month"`);
        await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "year"`);
        await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "esrsCategory"`);
        await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "documentType"`);
    }
}
exports.SchemaUpdate1732764177064 = SchemaUpdate1732764177064;
//# sourceMappingURL=1732764177064-schema-update.js.map