"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1733130410239 = void 0;
class SchemaUpdate1733130410239 {
    async up(queryRunner) {
        await queryRunner.query(`
        UPDATE esrs_topic SET name = 'E1 - Climate Change' WHERE id = 1;
        UPDATE esrs_topic SET name = 'E2 - Pollution' WHERE id = 2;
        UPDATE esrs_topic SET name = 'E3 - Water and Marine Resources' WHERE id = 3;
        UPDATE esrs_topic SET name = 'E4 - Biodiversity and Ecosystems' WHERE id = 4;
        UPDATE esrs_topic SET name = 'E5 - Resource Use and Circular Economy' WHERE id = 5;
        UPDATE esrs_topic SET name = 'S1 - Own Workforce' WHERE id = 6;
        UPDATE esrs_topic SET name = 'S2 - Workers in the Value Chain' WHERE id = 7;
        UPDATE esrs_topic SET name = 'S3 - Affected Communities' WHERE id = 8;
        UPDATE esrs_topic SET name = 'S4 - Consumers and End-users' WHERE id = 9;
        UPDATE esrs_topic SET name = 'G1 - Business Conduct' WHERE id = 10;`);
    }
    async down(queryRunner) {
        await queryRunner.query(`
        UPDATE esrs_topic SET name = 'Climate Change' WHERE id = 1;
        UPDATE esrs_topic SET name = 'Pollution' WHERE id = 2;
        UPDATE esrs_topic SET name = 'Water and Marine Resources' WHERE id = 3;
        UPDATE esrs_topic SET name = 'Biodiversity and Ecosystems' WHERE id = 4;
        UPDATE esrs_topic SET name = 'Resource Use and Circular Economy' WHERE id = 5;
        UPDATE esrs_topic SET name = 'Own Workforce' WHERE id = 6;
        UPDATE esrs_topic SET name = 'Workers in the Value Chain' WHERE id = 7;
        UPDATE esrs_topic SET name = 'Affected Communities' WHERE id = 8;
        UPDATE esrs_topic SET name = 'Consumers and End-users' WHERE id = 9;
        UPDATE esrs_topic SET name = 'Business Conduct' WHERE id = 10;`);
    }
}
exports.SchemaUpdate1733130410239 = SchemaUpdate1733130410239;
//# sourceMappingURL=1733130410239-schema-update.js.map