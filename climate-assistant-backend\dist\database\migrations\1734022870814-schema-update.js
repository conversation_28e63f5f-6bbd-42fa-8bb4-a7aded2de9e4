"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1734022870814 = void 0;
class SchemaUpdate1734022870814 {
    constructor() {
        this.name = 'SchemaUpdate1734022870814';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_topic" ADD "description" text`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_topic" DROP COLUMN "description"`);
    }
}
exports.SchemaUpdate1734022870814 = SchemaUpdate1734022870814;
//# sourceMappingURL=1734022870814-schema-update.js.map