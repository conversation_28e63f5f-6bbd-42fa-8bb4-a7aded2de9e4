"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1739121870143 = void 0;
class SchemaUpdate1739121870143 {
    constructor() {
        this.name = 'SchemaUpdate1739121870143';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "data_request" ADD "customUserRemark" text`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "data_request" DROP COLUMN "customUserRemark"`);
    }
}
exports.SchemaUpdate1739121870143 = SchemaUpdate1739121870143;
//# sourceMappingURL=1739121870143-schema-update.js.map