"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1731391666754 = void 0;
class SchemaUpdate1731391666754 {
    constructor() {
        this.name = 'SchemaUpdate1731391666754';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document_chunk" ALTER COLUMN "page" TYPE character varying USING "page"::text`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document_chunk" ALTER COLUMN "page" TYPE integer USING "page"::integer`);
    }
}
exports.SchemaUpdate1731391666754 = SchemaUpdate1731391666754;
//# sourceMappingURL=1731391666754-schema-update.js.map