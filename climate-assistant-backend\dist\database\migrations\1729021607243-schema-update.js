"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1729021607243 = void 0;
class SchemaUpdate1729021607243 {
    constructor() {
        this.name = 'SchemaUpdate1729021607243';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "data_request" ADD CONSTRAINT "FK_d05073e6e4886714a8cbbb72f03" FOREIGN KEY ("approvedBy") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "data_request" DROP CONSTRAINT "FK_d05073e6e4886714a8cbbb72f03"`);
    }
}
exports.SchemaUpdate1729021607243 = SchemaUpdate1729021607243;
//# sourceMappingURL=1729021607243-schema-update.js.map