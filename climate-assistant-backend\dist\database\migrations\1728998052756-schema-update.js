"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1728998052756 = void 0;
class SchemaUpdate1728998052756 {
    async up(queryRunner) {
        await queryRunner.query(`
            INSERT INTO esrs_topic (id, name) VALUES (1, 'Climate change');
            INSERT INTO esrs_topic (id, name) VALUES (2, 'Pollution');
            INSERT INTO esrs_topic (id, name) VALUES (3, 'Water and marine resources');
            INSERT INTO esrs_topic (id, name) VALUES (4, 'Biodiversity and ecosystems');
            INSERT INTO esrs_topic (id, name) VALUES (5, 'Circular economy');
            INSERT INTO esrs_topic (id, name) VALUES (6, 'Own workforce');
            INSERT INTO esrs_topic (id, name) VALUES (7, 'Workers in the value chain');
            INSERT INTO esrs_topic (id, name) VALUES (8, 'Affected communities');
            INSERT INTO esrs_topic (id, name) VALUES (9, 'Consumers and end-users');
            INSERT INTO esrs_topic (id, name) VALUES (10, 'Business conduct');
        `);
        await queryRunner.query(`
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (1, 1, 'E1.GOV-3', 'E1', 'Integration of sustainability-related performance in incentive schemes');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (2, 2, 'E1-1', 'E1', 'Transition plan for climate change mitigation');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (3, 3, 'E1.SBM-3', 'E1', 'Material impacts, risks and opportunities and their interaction with strategy and business model');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (4, 4, 'E1.IRO-1', 'E1', 'Description of the processes to identify and assess material climate-related impacts, risks and opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (5, 5, 'E1-2', 'E1', 'Policies related to climate change mitigation and adaptation');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (6, 6, 'E1-3', 'E1', 'Actions and resources in relation to climate change policies');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (7, 7, 'E1-4', 'E1', 'Targets related to climate change mitigation and adaptation');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (8, 8, 'E1-5', 'E1', ' Energy consumption and mix');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (9, 9, 'E1-6', 'E1', 'Gross Scopes 1, 2, 3 and Total GHG emissions');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (10, 10, 'E1-7', 'E1', 'GHG removals and GHG mitigation projects financed through carbon credits');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (11, 11, 'E1-8', 'E1', 'Internal carbon pricing');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (12, 12, 'E1-9', 'E1', 'Anticipated financial effects from material physical and transition risks and potential climate-related opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (13, 13, 'E2.IRO-1', 'E2', 'Description of the processes to identify and assess material pollution-related impacts, risks and opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (14, 14, 'E2-1', 'E2', 'Policies related to pollution');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (15, 15, 'E2-2', 'E2', 'Actions and resources related to pollution');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (16, 16, 'E2-3', 'E2', 'Targets related to pollution');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (17, 17, 'E2-4', 'E2', 'Pollution of air, water and soil');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (18, 18, 'E2-5', 'E2', 'Substances of concern and substances of very high concern');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (19, 19, 'E2-6', 'E2', 'Anticipated financial effects from pollution-related impacts, risks and opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (20, 20, 'E3.IRO-1', 'E3', 'Description of the processes to identify and assess material water and marine resources-related impacts, risks and opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (21, 21, 'E3-1', 'E3', 'Policies related to water and marine resources');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (22, 22, 'E3-2', 'E3', 'Actions and resources related to water and marine resources');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (23, 23, 'E3-3', 'E3', 'Targets related to water and marine resources');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (24, 24, 'E3-4', 'E3', 'Water consumption');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (25, 25, 'E3-5', 'E3', 'Anticipated financial effects from water and marine resources-related impacts, risks and opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (26, 26, 'E4.SBM-3', 'E4', 'Material impacts, risks and opportunities and their interaction with strategy and business model');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (27, 27, 'E4.IRO-1', 'E4', 'Description of processes to identify and assess material biodiversity and ecosystem-related impacts, risks and opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (28, 28, 'E4-1', 'E4', 'Transition plan and consideration of biodiversity and ecosystems in strategy and business model');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (29, 29, 'E4-2', 'E4', 'Policies related to biodiversity and ecosystems');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (30, 30, 'E4-3', 'E4', 'Actions and resources related to biodiversity and ecosystems');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (31, 31, 'E4-4', 'E4', 'Targets related to biodiversity and ecosystems');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (32, 32, 'E4-5', 'E4', 'Impact metrics related to biodiversity and ecosystems change');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (33, 33, 'E4-6', 'E4', 'Anticipated financial effects from biodiversity and ecosystem-related risks and opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (34, 34, 'E5.IRO-1', 'E5', 'Description of the processes to identify and assess material resource use and circular economy-related impacts, risks and opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (35, 35, 'E5-1', 'E5', 'Policies related to resource use and circular economy');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (36, 36, 'E5-2', 'E5', 'Actions and resources related to resource use and circular economy');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (37, 37, 'E5-3', 'E5', 'Targets related to resource use and circular economy');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (38, 38, 'E5-4', 'E5', 'Resource inflows');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (39, 39, 'E5-5', 'E5', 'Resource outflows');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (40, 40, 'E5-6', 'E5', 'Anticipated financial effects from resource use and circular economy-related impacts, risks and opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (41, 41, 'S1.SBM-3', 'S1', 'Material impacts, risks and opportunities and their interaction with strategy and business model');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (42, 42, 'S1-1', 'S1', 'Policies related to own workforce');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (43, 43, 'S1-2', 'S1', 'Processes for engaging with own workforce and workers’ representatives about impacts');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (44, 44, 'S1-3', 'S1', 'Processes to remediate negative impacts and channels for own workforce to raise concerns');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (45, 45, 'S1-4', 'S1', 'Taking action on material impacts on own workforce, and approaches to managing material risks and pursuing material opportunities related to own workforce, and effectiveness of those actions');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (46, 46, 'S1-5', 'S1', 'Targets related to managing material negative impacts, advancing positive impacts, and managing material risks and opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (47, 47, 'S1-6', 'S1', 'Characteristics of the undertaking’s employees');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (48, 48, 'S1-7', 'S1', 'Characteristics of non-employees in the undertaking’s own workforce');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (49, 49, 'S1-8', 'S1', 'Collective bargaining coverage and social dialogue');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (50, 50, 'S1-9', 'S1', 'Diversity metrics');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (51, 51, 'S1-10', 'S1', 'Adequate wages');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (52, 52, 'S1-11', 'S1', 'Social protection');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (53, 53, 'S1-12', 'S1', 'Persons with disabilities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (54, 54, 'S1-13', 'S1', 'Training and skills development metrics');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (55, 55, 'S1-14', 'S1', 'Health and safety metrics');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (56, 56, 'S1-15', 'S1', 'Work-life balance metrics');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (57, 57, 'S1-16', 'S1', 'Remuneration metrics (pay gap and total remuneration)');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (58, 58, 'S1-17', 'S1', 'Incidents, complaints and severe human rights impacts');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (59, 59, 'S2.SBM-3', 'S2', 'Material impacts, risks and opportunities and their interaction with strategy and business model');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (60, 60, 'S2-1', 'S2', 'Policies related to value chain workers');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (61, 61, 'S2-2', 'S2', 'Processes for engaging with value chain workers about impacts');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (62, 62, 'S2-3', 'S2', 'Processes to remediate negative impacts and channels for value chain workers to raise concerns');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (63, 63, 'S2-4', 'S2', 'Taking action on material impacts on value chain workers, and approaches to managing material risks and pursuing material opportunities related to value chain workers, and effectiveness of those action');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (64, 64, 'S2-5', 'S2', 'Targets related to managing material negative impacts, advancing positive impacts, and managing material risks and opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (65, 65, 'S3.SBM-3', 'S3', 'Material impacts, risks and opportunities and their interaction with strategy and business model');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (66, 66, 'S3-1', 'S3', 'Policies related to affected communities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (67, 67, 'S3-2', 'S3', 'Processes for engaging with affected communities about impacts');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (68, 68, 'S3-3', 'S3', 'Processes to remediate negative impacts and channels for affected communities to raise concerns');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (69, 69, 'S3-4', 'S3', 'Taking action on material impacts on affected communities, and approaches to managing material risks and pursuing material opportunities related to affected communities, and effectiveness of those actions');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (70, 70, 'S3-5', 'S3', 'Targets related to managing material negative impacts, advancing positive impacts, and managing material risks and opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (71, 71, 'S4.SBM-3', 'S4', 'Material impacts, risks and opportunities and their interaction with strategy and business model');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (72, 72, 'S4-1', 'S4', 'Policies related to consumers and end-users');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (73, 73, 'S4-2', 'S4', 'Processes for engaging with consumers and end-users about impacts');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (74, 74, 'S4-3', 'S4', 'Processes to remediate negative impacts and channels for consumers and end-users to raise concerns');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (75, 75, 'S4-4', 'S4', 'Taking action on material impacts on consumers and end-users, and approaches to managing material risks and pursuing material opportunities related to consumers and end-users, and effectiveness of those actions');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (76, 76, 'S4-5', 'S4', 'Targets related to managing material negative impacts, advancing positive impacts, and managing material risks and opportunities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (77, 77, 'G1.GOV-1', 'G1', 'The role of the administrative, supervisory and management bodies');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (78, 78, 'G1-1', 'G1', 'Business conduct policies and corporate culture');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (79, 79, 'G1-2', 'G1', 'Management of relationships with suppliers');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (80, 80, 'G1-3', 'G1', 'Prevention and detection of corruption and bribery');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (81, 81, 'G1-4', 'G1', 'Incidents of corruption or bribery');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (82, 82, 'G1-5', 'G1', 'Political influence and lobbying activities');
            INSERT INTO esrs_disclosure_requirement (id, sort, dr, esrs, name) VALUES (83, 83, 'G1-6', 'G1', 'Payment practices');
        `);
        await queryRunner.query(`
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (1, 1);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (1, 2);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (1, 3);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (1, 4);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (1, 5);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (1, 6);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (1, 7);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (1, 8);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (1, 9);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (1, 10);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (1, 11);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (1, 12);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (2, 13);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (2, 14);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (2, 15);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (2, 16);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (2, 17);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (2, 18);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (2, 19);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (3, 20);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (3, 21);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (3, 22);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (3, 23);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (3, 24);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (3, 25);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (4, 26);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (4, 27);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (4, 28);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (4, 29);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (4, 30);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (4, 31);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (4, 32);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (4, 33);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (5, 34);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (5, 35);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (5, 36);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (5, 37);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (5, 38);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (5, 39);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (5, 40);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 41);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 42);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 43);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 44);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 45);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 46);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 47);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 48);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 49);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 50);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 51);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 52);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 53);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 54);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 55);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 56);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 57);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (6, 58);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (7, 59);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (7, 60);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (7, 61);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (7, 62);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (7, 63);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (7, 64);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (8, 65);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (8, 66);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (8, 67);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (8, 68);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (8, 69);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (8, 70);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (9, 71);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (9, 72);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (9, 73);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (9, 74);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (9, 75);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (9, 76);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (10, 77);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (10, 78);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (10, 79);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (10, 80);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (10, 81);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (10, 82);
            INSERT INTO esrs_topic_disclosure_requirement ("esrsTopicId", "esrsDisclosureRequirementId") VALUES (10, 83);
        `);
        await queryRunner.query(`
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (1,'E1.GOV-3_01', 'Disclosure of whether and how climate-related considerations are factored into remuneration of members of administrative, management and supervisory bodies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (2,'E1.GOV-3_02', 'Percentage of remuneration recognised that is linked to climate related considerations');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (3,'E1.GOV-3_03', 'Explanation of climate-related considerations that are factored into remuneration of members of administrative, management and supervisory bodies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (4,'E1-1_01', 'Disclosure of transition plan  for climate change mitigation');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (5,'E1-1_02', 'Explanation of how targets are compatible with limiting of global warming to one and half degrees Celsius in line with Paris Agreement');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (6,'E1-1_03', 'Disclosure of decarbonisation levers and key action');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (7,'E1-1_04', 'Disclosure of significant operational expenditures (Opex) and (or) capital expenditures (Capex) required for implementation of action plan');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (8,'E1-1_05', 'Financial resources allocated to action plan (OpEx)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (9,'E1-1_06', 'Financial resources allocated to action plan (CapEx)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (10,'E1-1_07', 'Explanation of potential locked-in GHG emissions from key assets and products and of how locked-in GHG emissions may jeopardise achievement of GHG emission reduction targets and drive transition risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (11,'E1-1_08', 'Explanation of any objective or plans (CapEx, CapEx plans, OpEx) for aligning economic activities (revenues, CapEx, OpEx) with criteria established in Commission Delegated Regulation 2021/2139');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (12,'E1-1_09', 'Significant CapEx for coal-related economic activities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (13,'E1-1_10', 'Significant CapEx for oil-related economic activities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (14,'E1-1_11', 'Significant CapEx for gas-related economic activities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (15,'E1-1_12', 'Undertaking is excluded from EU Paris-aligned Benchmarks');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (16,'E1-1_13', 'Explanation of how transition plan is embedded in and aligned with overall business strategy and financial planning');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (17,'E1-1_14', 'Transition plan is approved by administrative, management and supervisory bodies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (18,'E1-1_15', 'Explanation of progress in implementing transition plan');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (19,'E1-1_16', 'Date of adoption of transition plan for undertakings not having adopted transition plan yet');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (20,'E1.SBM-3_01', 'Type of climate-related risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (21,'E1.SBM-3_02', 'Description of scope of resilience analysis');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (22,'E1.SBM-3_03', 'Disclosure of how resilience analysis has been conducted');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (23,'E1.SBM-3_04', 'Disclosure of how resilience analysis has been conducted');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (24,'E1.SBM-3_05', 'Time horizons applied for resilience analysis');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (25,'E1.SBM-3_06', 'Description of results of resilience analysis');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (26,'E1.SBM-3_07', 'Description of ability to adjust or adapt strategy and business model to climate change');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (27,'E1.IRO-1_01', 'Description of process in relation to impacts on climate change');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (28,'E1.IRO-1_02', 'Description of process in relation to climate-related physical risks in own operations and along value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (29,'E1.IRO-1_03', 'Climate-related hazards have been identified over short-, medium- and long-term time horizons');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (30,'E1.IRO-1_04', 'Undertaking has screened whether assets and business activities may be exposed to climate-related hazards');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (31,'E1.IRO-1_05', 'Short-, medium- and long-term time horizons have been defined');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (32,'E1.IRO-1_06', 'Extent to which assets and business activities may be exposed and are sensitive to identified climate-related hazards has been assessed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (33,'E1.IRO-1_07', 'Identification of climate-related hazards and assessment of exposure and sensitivity are informed by high emissions climate scenarios');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (34,'E1.IRO-1_08', 'Explanation of how climate-related scenario analysis has been used to inform identification and assessment of physical risks over short, medium and long-term');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (35,'E1.IRO-1_09', 'Description of process in relation to climate-related transition risks and opportunities in own operations and along value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (36,'E1.IRO-1_10', 'Transition events have been identified over short-, medium- and long-term time horizons');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (37,'E1.IRO-1_11', 'Undertaking has screened whether assets and business activities may be exposed to transition events');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (38,'E1.IRO-1_12', 'Extent to which assets and business activities may be exposed and are sensitive to identified transition events has been assessed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (39,'E1.IRO-1_13', 'Identification of transition events and assessment of exposure has been informed by climate-related scenario analysis');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (40,'E1.IRO-1_14', 'Assets and business activities that are incompatible with or need significant efforts to be compatible with transition to climate-neutral economy have been identified');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (41,'E1.IRO-1_15', 'Explanation of how climate-related scenario analysis has been used to inform identification and assessment of transition risks and opportunities over short, medium and long-term');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (42,'E1.IRO-1_16', 'Explanation of how climate scenarios used are compatible with critical climate-related assumptions made in financial statements');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (43,'E1.MDR-P_01-06', 'Policies in place to manage its material impacts, risks and opportunities related to climate change mitigation and adaptation [see ESRS 2 MDR-P]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (44,'E1-2_01', 'Sustainability matters addressed by policy for climate change');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (45,'E1.MDR-P_07-08', 'Disclosures to be reported in case the undertaking has not adopted policies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (46,'E1.MDR-A_01-12', 'Actions and Resources related to climate change mitigation and adaptation [see ESRS 2 MDR-A]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (47,'E1-3_01', 'Decarbonisation lever type');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (48,'E1-3_02', 'Adaptation solution type');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (49,'E1-3_03', 'Achieved GHG emission reductions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (50,'E1-3_04', 'Expected GHG emission reductions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (51,'E1-3_05', 'Explanation of extent to which ability to implement action depends on availability and allocation of resources');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (52,'E1-3_06', 'Explanation of relationship of significant CapEx and OpEx required to implement actions taken or planned to relevant line items or notes in financial statements');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (53,'E1-3_07', 'Explanation of relationship of significant CapEx and OpEx required to implement actions taken or planned to key performance indicators required under Commission Delegated Regulation (EU) 2021/2178');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (54,'E1-3_08', 'Explanation of relationship of significant CapEx and OpEx required to implement actions taken or planned to CapEx plan required by Commission Delegated Regulation (EU) 2021/2178');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (55,'E1.MDR-A_13-14', 'Disclosure to be reported if the undertaking has not adopted actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (56,'E1.MDR-T_01-13', 'Tracking effectiveness of policies and actions through targets [see ESRS 2 MDR-T ]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (57,'E1-4_01', 'Disclosure of whether and how GHG emissions reduction targets and (or) any other targets have been set to manage material climate-related impacts, risks and opportunities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (58,'E1-4_02', 'Tables: Multiple Dimensions (baseline year and targets; GHG Types, Scope 3 Categories, Decarbonisation levers, entity-specific denominators for intensity value)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (59,'E1-4_03', 'Absolute value of total Greenhouse gas emissions reduction');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (60,'E1-4_04', 'Percentage of total Greenhouse gas emissions reduction (as of emissions of base year)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (61,'E1-4_05', 'Intensity value of total Greenhouse gas emissions reduction');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (62,'E1-4_06', 'Absolute value of Scope 1 Greenhouse gas emissions reduction');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (63,'E1-4_07', 'Percentage of Scope 1 Greenhouse gas emissions reduction (as of emissions of base year)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (64,'E1-4_08', 'Intensity value of Scope 1 Greenhouse gas emissions reduction');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (65,'E1-4_09', 'Absolute value of location-based Scope 2 Greenhouse gas emissions reduction');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (66,'E1-4_10', 'Percentage of location-based Scope 2 Greenhouse gas emissions reduction (as of emissions of base year)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (67,'E1-4_11', 'Intensity value of location-based Scope 2 Greenhouse gas emissions reduction');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (68,'E1-4_12', 'Absolute value of market-based Scope 2 Greenhouse gas emissions reduction');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (69,'E1-4_13', 'Percentage of market-based Scope 2 Greenhouse gas emissions reduction (as of emissions of base year)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (70,'E1-4_14', 'Intensity value of market-based Scope 2 Greenhouse gas emissions reduction');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (71,'E1-4_15', 'Absolute value of Scope 3 Greenhouse gas emissions reduction');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (72,'E1-4_16', 'Percentage of Scope 3 Greenhouse gas emissions reduction (as of emissions of base year)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (73,'E1-4_17', 'Intensity value of Scope 3 Greenhouse gas emissions reduction');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (74,'E1-4_18', 'Explanation of how consistency of GHG emission reduction targets with GHG inventory boundaries has been ensured');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (75,'E1-4_19', 'Disclosure of past progress made in meeting target before current base year');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (76,'E1-4_20', 'Description of how it has been ensured that baseline value is representative in terms of activities covered and influences from external factors');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (77,'E1-4_21', 'Description of how new baseline value affects new target, its achievement and presentation of progress over time');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (78,'E1-4_22', 'GHG emission reduction target is science based and compatible with limiting global warming to one and half degrees Celsius');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (79,'E1-4_23', 'Description of expected decarbonisation levers and their overall quantitative contributions to achieve GHG emission reduction target');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (80,'E1-4_24', 'Diverse range of climate scenarios have been considered to detect relevant environmental, societal, technology, market and policy-related developments and determine decarbonisation levers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (81,'E1.MDR-T_14-19', 'Disclosure to be reported if the undertaking has not set any measurable outcome-oriented targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (82,'E1-5_01', 'Total energy consumption related to own operations');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (83,'E1-5_02', 'Total energy consumption from fossil sources');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (84,'E1-5_03', 'Total energy consumption from nuclear sources');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (85,'E1-5_04', 'Percentage of energy consumption from nuclear sources in total energy consumption');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (86,'E1-5_05', 'Total energy consumption from renewable sources');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (87,'E1-5_06', 'Fuel consumption from renewable sources');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (88,'E1-5_07', 'Consumption of purchased or acquired electricity, heat, steam, and cooling from renewable sources');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (89,'E1-5_08', 'Consumption of self-generated non-fuel renewable energy');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (90,'E1-5_09', 'Percentage of renewable sources in total energy consumption');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (91,'E1-5_10', 'Fuel consumption from coal and coal products');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (92,'E1-5_11', 'Fuel consumption from crude oil and petroleum products');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (93,'E1-5_12', 'Fuel consumption from natural gas');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (94,'E1-5_13', 'Fuel consumption from other fossil sources');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (95,'E1-5_14', 'Consumption of purchased or acquired electricity, heat, steam, or cooling from fossil sources');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (96,'E1-5_15', 'Percentage of fossil sources in total energy consumption');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (97,'E1-5_16', 'Non-renewable energy production');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (98,'E1-5_17', 'Renewable energy production');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (99,'E1-5_18', 'Energy intensity from activities in high climate impact sectors (total energy consumption per net revenue)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (100,'E1-5_19', 'Total energy consumption from activities in high climate impact sectors');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (101,'E1-5_20', 'High climate impact sectors used to determine energy intensity');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (102,'E1-5_21', 'Disclosure of reconciliation to relevant line item or notes in financial statements of net revenue from activities in high climate impact sectors');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (103,'E1-5_22', 'Net revenue from activities in high climate impact sectors');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (104,'E1-5_23', 'Net revenue from activities other than in high climate impact sectors');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (105,'E1-6_01', 'Gross Scopes 1, 2, 3 and Total GHG emissions - GHG emissions per scope [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (106,'E1-6_02', 'Gross Scopes 1, 2, 3 and Total GHG emissions - financial and operational control [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (107,'E1-6_03', 'Disaggregation of GHG emissions - by country, operating segments, economic activity, subsidiary, GHG category or source type');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (108,'E1-6_04', 'Gross Scopes 1, 2, 3 and Total GHG emissions - Scope 3 GHG emissions (GHG Protocol) [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (109,'E1-6_05', 'Gross Scopes 1, 2, 3 and Total GHG emissions - Scope 3 GHG emissions (ISO 14064-1) [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (110,'E1-6_06', 'Gross Scopes 1, 2, 3 and Total GHG emissions - total GHG emissions - value chain [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (111,'E1-6_07', 'Gross Scope 1 greenhouse gas emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (112,'E1-6_08', 'Percentage of Scope 1 GHG emissions from regulated emission trading schemes');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (113,'E1-6_09', 'Gross location-based Scope 2 greenhouse gas emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (114,'E1-6_10', 'Gross market-based Scope 2 greenhouse gas emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (115,'E1-6_11', 'Gross Scope 3 greenhouse gas emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (116,'E1-6_12', 'Total GHG emissions location based');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (117,'E1-6_13', 'Total GHG emissions market based');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (118,'E1-6_14', 'Disclosure of significant changes in definition of what constitutes reporting undertaking and its value chain and explanation of their effect on year-to-year comparability of reported GHG emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (119,'E1-6_15', 'Disclosure of methodologies, significant assumptions and emissions factors used to calculate or measure GHG emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (120,'E1-6_16', 'Disclosure of the effects of significant events and changes in circumstances (relevant to its GHG emissions) that occur between the reporting dates of the entities in its value chain and the date of the undertaking’s general purpose financial statements');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (121,'E1-6_17', 'biogenic emissions of CO2 from the combustion or bio-degradation of biomassnot included in Scope 1 GHG emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (122,'E1-6_18', 'Percentage of contractual instruments, Scope 2 GHG emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (123,'E1-6_19', 'Disclosure of types of contractual instruments, Scope 2 GHG emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (124,'E1-6_20', 'Percentage of market-based Scope 2 GHG emissions linked to purchased electricity bundled with instruments');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (125,'E1-6_21', 'Percentage of contractual instruments used for sale and purchase of energy bundled with attributes about energy generation in relation to Scope 2 GHG emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (126,'E1-6_22', 'Percentage of contractual instruments used for sale and purchase of unbundled energy attribute claims in relation to Scope 2 GHG emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (127,'E1-6_23', 'Disclosure of types of contractual instruments used for sale and purchase of energy bundled with attributes about energy generation or for unbundled energy attribute claims');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (128,'E1-6_24', 'Biogenic emissions of CO2 from combustion or bio-degradation of biomass not included in Scope 2 GHG emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (129,'E1-6_25', 'Percentage of GHG Scope 3 calculated using primary data');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (130,'E1-6_26', 'Disclosure of why Scope 3 GHG emissions category has been excluded');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (131,'E1-6_27', 'List of Scope 3 GHG emissions categories included in inventory');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (132,'E1-6_28', 'Biogenic emissions of CO2 from combustion or bio-degradation of biomass that occur in value chain not included in Scope 3 GHG emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (133,'E1-6_29', 'Disclosure of reporting boundaries considered and calculation methods for estimating Scope 3 GHG emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (134,'E1-6_30', 'GHG emissions intensity, location-based (total GHG emissions per net revenue)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (135,'E1-6_31', 'GHG emissions intensity, market-based (total GHG emissions per net revenue)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (136,'E1-6_32', 'Disclosure of reconciliation to financial statements of net revenue used for calculation of GHG emissions intensity');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (137,'E1-6_33', 'Net revenue');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (138,'E1-6_34', 'Net revenue used to calculate GHG intensity');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (139,'E1-6_35', 'Net revenue other than used to calculate GHG intensity');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (140,'E1-7_01', 'Disclosure of GHG removals and storage resulting from projects developed in own operations or contributed to in upstream and downstream value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (141,'E1-7_02', 'Disclosure of GHG emission reductions or removals from climate change mitigation projects outside value chain financed or to be financed through any purchase of carbon credits');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (142,'E1-7_03', 'Removals and carbon credits are used');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (143,'E1-7_04', 'GHG Removals and storage Activity by undertaking scope (breakdown by own operations and value chain) and by removal and storage activity');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (144,'E1-7_05', 'Total GHG removals and storage');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (145,'E1-7_06', 'GHG emissions associated with removal activity');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (146,'E1-7_07', 'Reversals');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (147,'E1-7_08', 'Disclosure of calculation assumptions, methodologies and frameworks applied (GHG removals and storage)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (148,'E1-7_09', 'Removal activity has been converted into carbon credits and sold on to other parties on voluntary market');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (149,'E1-7_10', 'Total amount of carbon credits outside value chain that are verified against recognised quality standards and cancelled');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (150,'E1-7_11', 'Total amount of carbon credits outside value chain planned to be cancelled in future');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (151,'E1-7_12', 'Disclosure of extent of use and quality criteria used for carbon credits');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (152,'E1-7_13', 'Percentage of reduction projects');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (153,'E1-7_14', 'Percentage of removal projects');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (154,'E1-7_15', 'Type of carbon credits from removal projects');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (155,'E1-7_16', 'Percentage for recognised quality standard');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (156,'E1-7_17', 'Percentage issued from projects in European Union');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (157,'E1-7_18', 'Percentage that qualifies as corresponding adjustment');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (158,'E1-7_19', 'Date when carbon credits outside value chain are planned to be cancelled');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (159,'E1-7_20', 'Explanation of scope, methodologies and frameworks applied and how residual GHG emissions are intended to be neutralised');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (160,'E1-7_21', 'Public claims of GHG neutrality that involve use of carbon credits have been made');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (161,'E1-7_22', 'Public claims of GHG neutrality that involve use of carbon credits are accompanied by GHG emission reduction targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (162,'E1-7_23', 'Claims of GHG neutrality and reliance on carbon credits neither impede nor reduce achievement of GHG emission reduction targets or net zero target');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (163,'E1-7_24', 'Explanation of whether and how public claims of GHG neutrality that involve use of carbon credits are accompanied by GHG emission reduction targets and how claims of GHG neutrality and reliance on carbon credits neither impede nor reduce achievement of GHG emission reduction targets or net zero target');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (164,'E1-7_25', 'Explanation of credibility and integrity of carbon credits used');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (165,'E1-8_01', 'Carbon pricing scheme by type');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (166,'E1-8_02', 'Type of internal carbon pricing scheme');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (167,'E1-8_03', 'Description of specific scope of application of carbon pricing scheme');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (168,'E1-8_04', 'Carbon price applied for each metric tonne of greenhouse gas emission');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (169,'E1-8_05', 'Description of critical assumptions made to determine carbon price applied');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (170,'E1-8_06', 'Percentage of gross Scope 1 greenhouse gas emissions covered by internal carbon pricing scheme');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (171,'E1-8_07', 'Percentage of gross Scope 2 greenhouse gas emissions covered by internal carbon pricing scheme');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (172,'E1-8_08', 'Percentage of gross Scope 3 greenhouse gas emissions covered by internal carbon pricing scheme');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (173,'E1-8_09', 'Disclosure of whether and how carbon price used in internal carbon pricing scheme is consistent with carbon price used in financial statements');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (174,'E1-9_01', 'Assets at material physical risk before considering climate change adaptation actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (175,'E1-9_02', 'Assets at acute material physical risk before considering climate change adaptation actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (176,'E1-9_03', 'Assets at chronic material physical risk before considering climate change adaptation actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (177,'E1-9_04', 'Percentage of assets at material physical risk before considering climate change adaptation actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (178,'E1-9_05', 'Disclosure of location of significant assets at material physical risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (179,'E1-9_06', 'Disclosure of location of its significant assets at material physical risk (disaggregated by NUTS codes)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (180,'E1-9_07', 'Percentage of assets at material physical risk addressed by climate change adaptation actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (181,'E1-9_08', 'Net revenue from business activities at material physical risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (182,'E1-9_09', 'Percentage of net revenue from business activities at material physical risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (183,'E1-9_10', 'Disclosure of whether and how anticipated financial effects for assets and business activities at material physical risk have been assessed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (184,'E1-9_11', 'Disclosure of whether and how assessment of assets and business activities considered to be at material physical risk relies on or is part of process to determine material physical risk and to determine climate scenarios');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (185,'E1-9_12', 'Disclosure of risk factors for net revenue from business activities at material physical risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (186,'E1-9_13', 'Disclosure of magnitude of anticipated financial effects in terms of margin erosion for business activities at material physical risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (187,'E1-9_14', 'Assets at material transition risk before considering climate mitigation actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (188,'E1-9_15', 'Percentage of assets at material transition risk before considering climate mitigation actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (189,'E1-9_16', 'Percentage of assets at material transition risk addressed by climate change mitigation actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (190,'E1-9_17', 'Total carrying amount of real estate assets by energy efficiency classes');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (191,'E1-9_18', 'Disclosure of whether and how potential effects on future financial performance and position for assets and business activities at material transition risk have been assessed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (192,'E1-9_19', 'Disclosure of whether and how assessment of assets and business activities considered to be at material transition risk relies on or is part of process to determine material transition risks and to determine scenarios');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (193,'E1-9_20', 'Estimated amount of potentially stranded assets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (194,'E1-9_21', 'Percentage of estimated share of potentially stranded assets of total assets at material transition risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (195,'E1-9_22', 'Total carrying amount of real estate assets for which energy consumption is based on internal estimates');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (196,'E1-9_23', 'Liabilities from material transition risks that may have to be recognised in financial statements');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (197,'E1-9_24', 'Number of Scope 1 GHG emission allowances within regulated emission trading schemes');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (198,'E1-9_25', 'Number of emission allowances stored (from previous allowances) at beginning of reporting period');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (199,'E1-9_26', 'Potential future liabilities, based on existing contractual agreements, associated with carbon credits planned to be cancelled in near future');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (200,'E1-9_27', 'Monetised gross Scope 1 and 2 GHG emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (201,'E1-9_28', 'Monetised total GHG emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (202,'E1-9_29', 'Net revenue from business activities at material transition risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (203,'E1-9_30', 'Net revenue from customers operating in coal-related activities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (204,'E1-9_31', 'Net revenue from customers operating in oil-related activities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (205,'E1-9_32', 'Net revenue from customers operating in gas-related activities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (206,'E1-9_33', 'Percentage of net revenue from customers operating in coal-related activities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (207,'E1-9_34', 'Percentage of net revenue from customers operating in oil-related activities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (208,'E1-9_35', 'Percentage of net revenue from customers operating in gas-related activities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (209,'E1-9_36', 'Percentage of net revenue from business activities at material transition risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (210,'E1-9_37', 'Disclosure of risk factors for net revenue from business activities at material transition risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (211,'E1-9_38', 'Disclosure of anticipated financial effects in terms of margin erosion for business activities at material transition risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (212,'E1-9_39', 'Disclosure of reconciliations with financial statements of significant amounts of assets and net revenue at material physical risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (213,'E1-9_40', 'Disclosure of reconciliations with financial statements of significant amounts of assets, liabilities and net revenue at material transition risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (214,'E1-9_41', 'Expected cost savings from climate change mitigation actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (215,'E1-9_42', 'Expected cost savings from climate change adaptation actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (216,'E1-9_43', 'Potential market size of low-carbon products and services or adaptation solutions to which undertaking has or may have access');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (217,'E1-9_44', 'Expected changes to net revenue from low-carbon products and services or adaptation solutions to which undertaking has or may have access');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (218,'E2.IRO-1_01', 'Information about the process to identify actual and potential pollution-related impacts, risks and opportuntities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (219,'E2.IRO-1_02', 'Disclosure of whether and how consultations have been conducted (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (220,'E2.IRO-1_03', 'Disclosure of results of materiality assessment (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (221,'E2.MDR-P_01-06', 'Policies to manage its material impacts, risks and opportunities related to pollution [see ESRS 2 MDR-P]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (222,'E2-1_01', 'Disclosure of whether and how policy addresses mitigating negative impacts related to pollution of air, water and soil');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (223,'E2-1_02', 'Disclosure of  whether and how policy addresses substituting and minimising use of substances of concern and phasing out substances of very high concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (224,'E2-1_03', 'Disclosure of  whether and how policy addresses avoiding incidents and emergency situations, and if and when they occur, controlling and limiting their impact on people and environment');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (225,'E2-1_04', 'Disclosure of contextual information on relations between policies implemented and how policies contribute to EU Action Plan Towards Zero Pollution for Air, Water and Soil');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (226,'E2.MDR-P_07-08', 'Disclosures to be reported in case the undertaking has not adopted policies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (227,'E2.MDR-A_01-12', 'Actions and resources in relation to pollution [see ESRS 2 MDR-A]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (228,'E2-2_01', 'Layer in mitigation hierarchy to which action can be allocated to (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (229,'E2-2_02', 'Action related to pollution extends to upstream/downstream value chain engagements');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (230,'E2-2_03', 'Layer in mitigation hierarchy to which resources can be allocated to (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (231,'E2-2_04', 'Information about action plans that have been implemented at site-level (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (232,'E2.MDR-A_13-14', 'Disclosures to be reported if the undertaking has not adopted actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (233,'E2.MDR-T_01-13', 'Tracking effectiveness of policies and actions through targets [see ESRS 2 MDR-T ]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (234,'E2-3_01', 'Disclosure of  whether and  how target relates to prevention and control of air pollutants and respective specific loads');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (235,'E2-3_02', 'Disclosure of  whether and how target relates to prevention and control of emissions to water and respective specific loads');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (236,'E2-3_03', 'Disclosure of  whether and how target relates to prevention and control of pollution to soil and respective specific loads');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (237,'E2-3_04', 'Disclosure of  whether and how target relates to prevention and control of  substances of concern and substances of very high concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (238,'E2-3_05', 'Ecological thresholds and entity-specific allocations were taken into consideration when setting pollution-related target');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (239,'E2-3_06', 'Disclosure of ecological thresholds identified and methodology used to identify ecological thresholds (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (240,'E2-3_07', 'Disclosure of how ecological entity-specific thresholds were determined (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (241,'E2-3_08', 'Disclosure of how responsibility for respecting identified ecological thresholds is allocated (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (242,'E2-3_09', 'Pollution-related target is mandatory (required by legislation)/voluntary');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (243,'E2-3_10', 'Pollution-related target addresses shortcomings related to Substantial Contribution criteria for Pollution Prevention and Control');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (244,'E2-3_11', 'Information about targets that have been implemented at site-level (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (245,'E2.MDR-T_14-19', 'Disclosures to be reported if the undertaking has not adopted targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (246,'E2-4_01', 'Pollution of air, water and soil [multiple dimensions: at site level or  by type of source, by sector or by geographical area');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (247,'E2-4_02', 'Emissions to air by pollutant');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (248,'E2-4_03', 'Emissions to water by pollutant  [+ by sectors/Geographical Area/Type of source/Site location]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (249,'E2-4_04', 'Emissions to soil by pollutant  [+ by sectors/Geographical Area/Type of source/Site location]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (250,'E2-4_05', 'Microplastics generated and used');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (251,'E2-4_06', 'Microplastics generated');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (252,'E2-4_07', 'Microplastics used');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (253,'E2-4_08', 'Description of changes over time (pollution of air, water and soil)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (254,'E2-4_09', 'Description of measurement methodologies (pollution of air, water and soil)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (255,'E2-4_10', 'Description of process(es) to collect data for pollution-related accounting and reporting');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (256,'E2-4_11', 'Percentage of total emissions of pollutants to water occurring in areas at water risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (257,'E2-4_12', 'Percentage of total emissions of pollutants to water occurring in areas of high-water stress');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (258,'E2-4_13', 'Percentage of total emissions of pollutants to soil occurring in areas at water risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (259,'E2-4_14', 'Percentage of total emissions of pollutants to soil occurring in areas of high-water stress');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (260,'E2-4_15', 'Disclosure of reasons for choosing inferior methodology to quantify emissions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (261,'E2-4_16', 'Disclosure of list of installations operated that fall under IED and EU BAT Conclusions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (262,'E2-4_17', 'Disclosure of list of any non-compliance incidents or enforcement actions necessary to ensure compliance in case of breaches of permit conditions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (263,'E2-4_18', 'Disclosure of actual performance and comparison of environmental performance against emission levels associated with best available techniques (BAT-AEL) as described in EU-BAT conclusions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (264,'E2-4_19', 'Disclosure of actual performance against environmental performance levels associated with best available techniques (BAT-AEPLs) applicable to sector and installation');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (265,'E2-4_20', 'Disclosure of list of any compliance schedules or derogations granted by competent authorities according to Article 15(4) IED that are associated with implementation of BAT-AELs');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (266,'E2-5_01', 'Total amount of substances of concern that are generated or used during production or that are procured, breakdown by main hazard classes of substances of concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (267,'E2-5_02', 'Total amount of substances of concern that are generated or used during production or that are procured');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (268,'E2-5_03', 'Total amount of substances of concern that leave facilities as emissions, as products, or as part of products or services');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (269,'E2-5_04', 'Amount of substances of concern that leave facilities as emissions by main hazard classes of substances of concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (270,'E2-5_05', 'Amount of substances of concern that leave facilities as products by main hazard classes of substances of concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (271,'E2-5_06', 'Amount of substances of concern that leave facilities as part of products by main hazard classes of substances of concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (272,'E2-5_07', 'Amount of substances of concern that leave facilities as services by main hazard classes of substances of concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (273,'E2-5_08', 'Total amount of substances of very high concern that are generated or used during production or that are procured by main hazard classes of substances of concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (274,'E2-5_09', 'Total amount of substances of very high concern that leave facilities as emissions, as products, or as part of products or services by main hazard classes of substances of concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (275,'E2-5_10', 'Amount of substances of very high concern that leave facilities as emissions by main hazard classes of substances of concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (276,'E2-5_11', 'Amount of substances of very high concern that leave facilities as products by main hazard classes of substances of concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (277,'E2-5_12', 'Amount of substances of very high concern that leave facilities as part of products by main hazard classes of substances of concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (278,'E2-5_13', 'Amount of substances of very high concern that leave facilities as services by main hazard classes of substances of concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (279,'E2-6_01', 'Disclosure of quantitative information about anticipated financial effects of material risks and opportunities arising from pollution-related impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (280,'E2-6_02', 'Percentage of net revenue made with products and services that are or that contain substances of concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (281,'E2-6_03', 'Percentage of net revenue made with products and services that are or that contain substances of very high concern');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (282,'E2-6_04', 'Operating expenditures (OpEx) in conjunction with major incidents and deposits (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (283,'E2-6_05', 'Capital expenditures (CapEx) in conjunction with major incidents and deposits (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (284,'E2-6_06', 'Provisions for environmental protection and remediation costs (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (285,'E2-6_07', 'Disclosure of qualitative information about anticipated financial effects of material risks and opportunities arising from pollution-related impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (286,'E2-6_08', 'Description of effects considered, related impacts and time horizons in which they are likely to materialise (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (287,'E2-6_09', 'Disclosure of critical assumptions used to quantify anticipated financial effects, sources and level of uncertainty of assumptions (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (288,'E2-6_10', 'Description of material incidents and deposits whereby pollution had negative impacts on environment and (or) is expected to have negative effects on financial cash flows, financial position and financial performance');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (289,'E2-6_11', 'Disclosure of assessment of related products and services at risk and explanation how time horizon is defined, financial amounts are estimated, and which critical assumptions are made (pollution)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (290,'E3.IRO-1_01', 'Disclosure of whether and how assets and activities have been screened in order to identify actual and potential water and marine resources-related impacts, risks and opportunities in own operations and upstream and downstream value chain and methodologies, assumptions and tools used in screening [text block]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (291,'E3.IRO-1_02', 'Disclosure of how consultations have been conducted (water and marine resources) [text block]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (292,'E3.MDR-P_01-06', 'Policies to manage its material impacts, risks and opportunities related to water and marine resources [see ESRS 2 MDR-P]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (293,'E3-1_01', 'Disclosure of whether and how policy adresses water management');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (294,'E3-1_02', 'Disclosure of whether and how policy adresses the use and sourcing of water and marine resources in own operations');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (295,'E3-1_03', 'Disclosure of whether and  how policy adresses water treatment');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (296,'E3-1_04', 'Disclosure of whether and how policy adresses prevention and abatment of water pollution');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (297,'E3-1_05', 'Disclosure of whether and how policy adresses product and service design in view of addressing water-related issues and preservation of marine resources');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (298,'E3-1_06', 'Disclosure of whether and how policy adresses commitment to reduce material water consumption in areas at water risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (299,'E3-1_07', 'Disclosure of reasons for not having adopted policies in areas of high-water stress');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (300,'E3-1_08', 'Disclosure of timeframe in which policies in areas of high-water stress will be adopted');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (301,'E3-1_09', 'Policies or practices related to sustainable oceans and seas have been adopted');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (302,'E3-1_10', 'The policly contributes to good ecological and chemical quality of surface water bodies and good chemical quality and quantity of groundwater bodies, in order to protect human health, water supply, natural ecosystems and biodiversity, the good environmental status of marine waters and the protection of the resource base upon which marine related activities depend;');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (303,'E3-1_11', 'The policy minimise material impacts and risks and implement mitigation measures that aim to maintain the value and functionality of priority services and to increase resource efficiency on own operations');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (304,'E3-1_12', 'The policy avoid impacts on affected communities.');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (305,'E3.MDR-P_07-08', 'Disclosures to be reported in case the undertaking has not adopted policies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (306,'E3.MDR-A_01-12', 'Actions and resources in relation to water and marine resources [see ESRS 2 MDR-A]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (307,'E3-2_01', 'Layer in mitigation hierarchy to which action and resources can be allocated to (water and marine resources)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (308,'E3-2_02', 'Information about specific collective action for water and marine resources');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (309,'E3-2_03', 'Disclosure of actions and resources  in relation to areas at water risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (310,'E3.MDR-A_13-14', 'Disclosures to be reported if the undertaking has not adopted actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (311,'E3.MDR-T_01-13', 'Tracking effectiveness of policies and actions through targets [see ESRS 2 MDR-T ]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (312,'E3-3_01', 'Disclosure of whether and  how target relates to management of material impacts, risks and opportunities related to areas at water risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (313,'E3-3_02', 'Disclosure of whether and  how target relates to responsible management of marine resources impacts, risks and opportunities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (314,'E3-3_03', 'Disclosure of whether and how target relates to reduction of water consumption');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (315,'E3-3_04', '(Local) ecological threshold and entity-specific allocation were taken into consideration when setting water and marine resources target');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (316,'E3-3_05', 'Disclosure of ecological threshold identified and methodology used to identify ecological threshold (water and marine resources)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (317,'E3-3_06', 'Disclosure of how ecological entity-specific threshold was determined (water and marine resources)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (318,'E3-3_07', 'Disclosure of how responsibility for respecting identified ecological threshold is allocated (water and marine resources)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (319,'E3-3_08', 'Adopted and presented water and marine resources-related target is mandatory (based on legislation)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (320,'E3-3_09', 'Target relates to reduction of water withdrawals');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (321,'E3-3_10', 'Target relates to reduction of water discharges');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (322,'E3.MDR-T_14-19', 'Disclosures to be reported if the undertaking has not adopted targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (323,'E3-4_01', 'Total water consumption');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (324,'E3-4_02', 'Total water consumption in areas at water risk, including areas of high-water stress');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (325,'E3-4_03', 'Total water recycled and reused');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (326,'E3-4_04', 'Total water stored');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (327,'E3-4_05', 'Changes in water storage');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (328,'E3-4_06', 'Disclosure of contextual information regarding warter consumption');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (329,'E3-4_07', 'Share of the measure obtained from direct measurement, from sampling and extrapolation, or from best estimates');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (330,'E3-4_08', 'Water intensity ratio');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (331,'E3-4_09', 'Water consumption - sectors/SEGMENTS [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (332,'E3-4_10', 'Additional water intensity ratio');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (333,'E3-4_11', 'Total water withdrawals');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (334,'E3-4_12', 'Total water discharges');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (335,'E3-5_01', 'Disclosure of quantitative information about anticipated financial effects of material risks and opportunities arising from water and marine resources-related impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (336,'E3-5_02', 'Disclosure of qualitative information of anticipated financial effects of material risks and opportunities arising from water and marine resources-related impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (337,'E3-5_03', 'Description of effects considered and related impacts (water and marine resources)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (338,'E3-5_04', 'Disclosure of critical assumptions used in estimates of financial effects of material risks and opportunities arising from water and marine resources-related impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (339,'E3-5_05', 'Description of related products and services at risk (water and marine resources)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (340,'E3-5_06', 'Explanation of how time horizons are defined, financial amounts are estimated and critical assumptions made (water and marine resources)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (341,'E4.SBM-3_01', 'List of material sites in own operation');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (342,'E4.SBM-3_02', 'Disclosure of activities negatively affecting biodiversity sensitive areeas');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (343,'E4.SBM-3_03', 'Disclosure of list of material sites in own operations based on results of identification and assessment of actual and potential impacts on biodiversity and ecosystems');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (344,'E4.SBM-3_04', 'Disclosure of biodiversity-sensitive areas impacted');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (345,'E4.SBM-3_05', 'Material negative impacts with regards to land degradation, desertification or soil sealing have been identified');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (346,'E4.SBM-3_06', 'Own operations affect threatened species');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (347,'E4.IRO-1_01', 'Disclosure of whether and how actual and potential impacts on biodiversity and ecosystems at own site locations and in value chain have been identified and assessed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (348,'E4.IRO-1_02', 'Disclosure of whether and how dependencies on biodiversity and ecosystems and their services have been identified and assessed at own site locations and in value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (349,'E4.IRO-1_03', 'Disclosure of whether and how transition and physical risks and opportunities related to biodiversity and ecosystems have been identified and assessed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (350,'E4.IRO-1_04', 'Disclosure of whether and how systemic risks have been considered (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (351,'E4.IRO-1_05', 'Disclosure of whether and how consultations with affected communities on sustainability assessments of shared biological resources and ecosystems have been conducted');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (352,'E4.IRO-1_06', 'Disclosure of whether and how specific sites, raw materials production or sourcing with negative or potential negative impacts on affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (353,'E4.IRO-1_07', 'Disclosure of whether and how communities were involved in materiality assessment');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (354,'E4.IRO-1_08', 'Disclosure of whether and how negative impacts on priority ecosystem services of relevance to affected communities may be avoided');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (355,'E4.IRO-1_09', 'Disclosure of plans to minimise unavoidable negative impacts and implement mitigation measures that aim to maintain value and functionality of priority services');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (356,'E4.IRO-1_10', 'Disclosure of whether and how tthe business model(s) has been verified using range of biodiversity and ecosystems scenarios, or other scenarios with modelling of biodiversity and ecosystems related consequences, with different possible pathways');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (357,'E4.IRO-1_11', 'Disclosure of why considered scenarios were taken into consideration');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (358,'E4.IRO-1_12', 'Disclosure of how considered scenarios are updated according to evolving conditions and emerging trends');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (359,'E4.IRO-1_13', 'Scenarios are informed by expectations in authoritative intergovernmental instruments and by scientific consensus');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (360,'E4.IRO-1_14', 'Undertaking has sites located in or near biodiversity-sensitive areas');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (361,'E4.IRO-1_15', 'Activities related to sites located in or near biodiversity-sensitive areas negatively affect these areas by leading to deterioration of natural habitats and habitats of species and to disturbance of species for which protected area has been designated');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (362,'E4.IRO-1_16', 'It has been concluded that it is necessary to implement biodiversity mitigation measures');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (363,'E4-1_01', 'Disclosure of resilience of current business model(s) and strategy to biodiversity and ecosystems-related physical, transition and systemic risks and opportunities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (364,'E4-1_02', 'Disclosure of scope of resilience analysis along own operations and related upstream and downstream value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (365,'E4-1_03', 'Disclosure of key assumptions made (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (366,'E4-1_04', 'Disclosure of time horizons used for analysis (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (367,'E4-1_05', 'Disclosure of results of resilience analysis (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (368,'E4-1_06', 'Disclosure of involvement of stakeholders (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (369,'E4-1_07', 'Disclosure of transition plan to improve and achieve alignment of its business model and strategy');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (370,'E4-1_08', 'Explanation of how strategy and business model will be adjusted to improve and, ultimately, achieve alignment with relevant local, national and global public policy goals');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (371,'E4-1_09', 'Include information about  its own operations and  explain how it is responding to material impacts in its related value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (372,'E4-1_10', 'Explanation of how b strategy interacts with  transition plan');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (373,'E4-1_11', 'Disclosure of contribution to impact drivers and possible mitigation actions following mitigation hierarchy and main path-dependencies and locked-in assets and resources that are associated with biodiversity and ecosystems change');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (374,'E4-1_12', 'Explanation and quantification of investments and funding supporting the implementation of its transition plan');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (375,'E4-1_13', 'Disclosure of objectives or plans for aligning economic activities (revenues, CapEx)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (376,'E4-1_14', 'Biodiversity offsets are part of transition plan');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (377,'E4-1_15', 'Information about how process of implementing and updating transition plan is managed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (378,'E4-1_16', 'Indication of metrics and related tools used to measure progress that are integrated in measurement approach (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (379,'E4-1_17', 'Administrative, management and supervisory bodies have approved transition plan');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (380,'E4-1_18', 'Indication of current challenges and limitations to draft plan in relation to areas of significant impact and actions company is taking to address them (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (381,'E4.MDR-P_01-06', 'Policies to manage material impacts, risks, dependencies and opportunities related to biodiversity and ecosystems [see ESRS 2 - MDR-P]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (382,'E4-2_01', 'Disclosure on whether and how biodiversity and ecosystems-related policies relate to matters reported in E4 AR4');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (383,'E4-2_02', 'Explanation of whether and  how biodiversity and ecosystems-related policy relates to material biodiversity and ecosystems-related impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (384,'E4-2_03', 'Explanation of whether and  how biodiversity and ecosystems-related policy relates to material dependencies and material physical and transition risks and opportunities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (385,'E4-2_04', 'Explanation of whether and how biodiversity and ecosystems-related policy supports traceability of products, components and raw materials with significant actual or potential impacts on biodiversity and ecosystems along value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (386,'E4-2_05', 'Explanation of whether and how biodiversity and ecosystems-related policy addresses production, sourcing or consumption from ecosystems that are managed to maintain or enhance conditions for biodiversity');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (387,'E4-2_06', 'Explanation of whether and how biodiversity and ecosystems-related policy addresses social consequences of biodiversity and ecosystems-related impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (388,'E4-2_07', 'Disclosure of how policy refers to production, sourcing or consumption of raw materials');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (389,'E4-2_08', 'Disclosure of how policy refers to policies limiting procurement from suppliers that cannot demonstrate that they are not contributing to significant conversion of protected areas or key biodiversity areas');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (390,'E4-2_09', 'Disclosure of how policy refers to recognised standards or third-party certifications overseen by regulators');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (391,'E4-2_10', 'Disclosure of how policy addresses raw materials originating from ecosystems that have been managed to maintain or enhance conditions for biodiversity, as demonstrated by regular monitoring and reporting of biodiversity status and gains or losses');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (392,'E4-2_11', 'Disclosure of how the policy enables to a), b), c) and d)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (393,'E4-2_12', 'Third-party standard of conduct used in policy is objective and achievable based on scientific approach to identifying issues and realistic in assessing how these issues can be addressed under variety of practical circumstances');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (394,'E4-2_13', 'Third-party standard of conduct used in policy is developed or maintained through process of ongoing consultation with relevant stakeholders with balanced input from all relevant stakeholder groups with no group holding undue authority or veto power over content');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (395,'E4-2_14', 'Third-party standard of conduct used in policy encourages step-wise approach and continuous improvement in standard and its application of better management practices and requires establishment of meaningful targets and specific milestones to indicate progress against principles and criteria over time');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (396,'E4-2_15', 'Third-party standard of conduct used in policy is verifiable through independent certifying or verifying bodies, which have defined and rigorous assessment procedures that avoid conflicts of interest and are compliant with ISO guidance on accreditation and verification procedures or Article 5(2) of Regulation (EC) No 765/2008');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (397,'E4-2_16', 'Third-party standard of conduct used in policy conforms to ISEAL Code of Good Practice');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (398,'E4-2_17', 'Biodiversity and ecosystem protection policy covering operational sites owned, leased, managed in or near protected area or biodiversity-sensitive area outside protected areas has been adopted');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (399,'E4-2_18', 'Sustainable land or agriculture practices or policies have been adopted');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (400,'E4-2_19', 'Sustainable oceans or seas practices or policies have been adopted');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (401,'E4-2_20', 'Policies to address deforestation have been adopted');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (402,'E4.MDR-P_07-08', 'Disclosures to be reported in case the undertaking has not adopted policies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (403,'E4.MDR-A_01-12', 'Actions and resources in relation to biodiversity and ecosystems [see ESRS 2 - MDR-A]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (404,'E4-3_01', 'Disclosure on how the mitigation hierarchy has been applied with regard to biodiversity and ecosystem actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (405,'E4-3_02', 'Biodiversity offsets were used in action plan');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (406,'E4-3_03', 'Disclosure of aim of biodiversity offset and key performance indicators used');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (407,'E4-3_04', 'Financing effects (direct and indirect costs) of biodiversity offsets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (408,'E4-3_05', 'Explanation of rekationship of significant Capex and Opex required to impelement actions taken or planned to relevant line items or notes in the financial statements');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (409,'E4-3_06', 'Explanation of rekationship of significant Capex and Opex required to impelement actions taken or planned to key  performance indicators required under Commission Delegated Regulation (EU) 2021/2178');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (410,'E4-3_07', 'Explanation of rekationship of significant Capex and Opex required to impelement actions taken or planned to Capex plan required under Commission Delegated Regulation (EU) 2021/2178');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (411,'E4-3_08', 'Description of biodiversity offsets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (412,'E4-3_09', 'Description of whether and how local and indigenous knowledge and nature-based solutions have been incorporated into biodiversity and ecosystems-related action');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (413,'E4-3_10', 'Disclosure of key stakeholders involved and how they are involved, key stakeholders negatively or positively impacted by action and how they are impacted');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (414,'E4-3_11', 'Explanation of need for appropriate consultations and need to respect decisions of affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (415,'E4-3_12', 'Description of whether key action may induce significant negative sustainability impacts (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (416,'E4-3_13', 'Explanation of whether the key action is intended to be a one-time initiative or systematic practice');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (417,'E4-3_14', 'Key action plan is carried out only by undertaking (individual action) using its resources (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (418,'E4-3_15', 'Key action plan is part of wider action plan (collective action), of which undertaking is member (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (419,'E4-3_16', 'Additional information about project, its sponsors and other participants (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (420,'E4.MDR-A_13-14', 'Disclosures to be reported if the undertaking has not adopted actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (421,'E4.MDR-T_01-13', 'Tracking effectiveness of policies and actions through targets [see ESRS 2 MDR-T ]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (422,'E4-4_01', 'Ecological threshold and allocation of impacts to undertaking were applied when setting target (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (423,'E4-4_02', 'Disclosure of ecological threshold identified and methodology used to identify threshold (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (424,'E4-4_03', 'Disclosure of how entity-specific threshold was determined (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (425,'E4-4_04', 'Disclosure of how responsibility for respecting identified ecological threshold is allocated (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (426,'E4-4_05', 'Target is informed by relevant aspect of EU Biodiversity Strategy for 2030');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (427,'E4-4_06', 'Disclosure of how the targets relate to the biodiversity and ecosystem impacts, dependencies, risks and opportunities identified in relation to own operations and upstream and downstream value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (428,'E4-4_07', 'Disclosure of the geographical scope of the targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (429,'E4-4_08', 'Biodiversity offsets were used in setting target');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (430,'E4-4_09', 'Layer in mitigation hierarchy to which target can be allocated (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (431,'E4-4_10', 'The target addresses shortcomings related to the Substantial Contribution criteria');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (432,'E4.MDR-T_14-19', 'Disclosures to be reported if the undertaking has not adopted targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (433,'E4-5_01', 'Number of sites owned, leased or managed in or near protected areas or key biodiversity areas that undertaking is negatively affecting');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (434,'E4-5_02', 'Area of sites owned, leased or managed in or near protected areas or key biodiversity areas that undertaking is negatively affecting');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (435,'E4-5_03', 'Disclosure of land-use based on Life Cycle Assessment');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (436,'E4-5_04', 'Disclosure of metrics considered relevant (land-use change, freshwater-use change and (or) sea-use change)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (437,'E4-5_05', 'Disclosure of conversion over time of land cover');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (438,'E4-5_06', 'Disclosure of changes over time in management of ecosystem');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (439,'E4-5_07', 'Disclosure of changes in spatial configuration of landscape');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (440,'E4-5_08', 'Disclosure of changes in ecosystem structural connectivity');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (441,'E4-5_09', 'Disclosure of functional connectivity');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (442,'E4-5_10', 'Total use of land area');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (443,'E4-5_11', 'Total sealed area');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (444,'E4-5_12', 'Nature-oriented area on site');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (445,'E4-5_13', 'Nature-oriented area off site');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (446,'E4-5_14', 'Disclosure of how pathways of introduction and spread of invasive alien species and risks posed by invasive alien species are managed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (447,'E4-5_15', 'Number of invasive alien species');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (448,'E4-5_16', 'Area covered by invasive alien species');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (449,'E4-5_17', 'Disclosure of metrics considered relevant (state of species)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (450,'E4-5_18', 'Disclosure of paragraph in another environment-related standard in which metric is referred to');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (451,'E4-5_19', 'Disclosure of population size, range within specific ecosystems and extinction risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (452,'E4-5_20', 'Disclosure of changes in number of individuals of species within specific area');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (453,'E4-5_21', 'Information about species at global extinction risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (454,'E4-5_22', 'Disclosure of threat status of species and how activities or pressures may affect threat status');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (455,'E4-5_23', 'Disclosure of change in relevant habitat for threatened species as proxy for impact on local population''s extinction risk');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (456,'E4-5_24', 'Disclosure of ecosystem area coverage');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (457,'E4-5_25', 'Disclosure of quality of ecosystems relative to pre-determined reference state');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (458,'E4-5_26', 'Disclosure of multiple species within ecosystem');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (459,'E4-5_27', 'Disclosure of structural components of ecosystem condition');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (460,'E4-6_01', 'Disclosure of quantitative information about anticipated financial effects of material risks and opportunities arising from biodiversity- and ecosystem-related impacts and dependencies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (461,'E4-6_02', 'Disclosure of qualitative information about anticipated financial effects of material risks and opportunities arising from biodiversity- and ecosystem-related impacts and dependencies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (462,'E4-6_03', 'Description of effects considered, related impacts and dependencies (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (463,'E4-6_04', 'Disclosure of critical assumptions used in estimates of financial effects of material risks and opportunities arising from biodiversity- and ecosystem-related impacts and dependencies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (464,'E4-6_05', 'Description of related products and services at risk (biodiversity and ecosystems) over the short-, medium- and long-term');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (465,'E4-6_06', 'Explanation of how financial amounts are estimated and critical assumptions made (biodiversity and ecosystems)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (466,'E5.IRO-1_01', 'Disclosure of whether the undertaking has screened its assets and activities in order to identify actual and potential impacts, risks and opportunities in own operations and upstream and downstream value chain, and if so, methodologies, assumptions and tools used');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (467,'E5.IRO-1_02', 'Disclosure of whether and how the undertaking has conducted consultations (resource and circular economy)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (468,'E5.MDR-P_01-06', 'Policies to manage its material impacts, risks and opportunities related to resource use and circular economy [see ESRS 2 MDR-P]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (469,'E5-1_01', 'Disclosure of whether and how policy addresses transitioning away from use of virgin resources, including relative increases in use of secondary (recycled) resources');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (470,'E5-1_02', 'Disclosure of whether and how policy addresses sustainable sourcing and use of renewable resources');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (471,'E5-1_03', 'Description of whether and how policy addresses waste hierarchy (prevention, preparing for re-use, recycling, other recovery, disposal)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (472,'E5-1_04', 'Description of  whether and how policy addresses prioritisation of strategies to avoid or minimise waste over waste treatment strategies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (473,'E5.MDR-P_07-08', 'Disclosures to be reported in case the undertaking has not adopted policies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (474,'E5.MDR-A_01-12', 'Actions and resources in relation to resource use and circular economy [see ESRS 2 MDR-A]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (475,'E5-2_01', 'Description of higher levels of resource efficiency in use of technical and biological materials and water');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (476,'E5-2_02', 'Description of higher rates of use of secondary raw materials');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (477,'E5-2_03', 'Description of application of circular design');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (478,'E5-2_04', 'Description of application of circular business practices');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (479,'E5-2_05', 'Description of actions taken to prevent waste generation in the undertaking''s upstream and downstram value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (480,'E5-2_06', 'Description of Optimistation of waste management');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (481,'E5-2_07', 'Information about collective action on development of collaborations or initiatives increasing circularity of products and materials');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (482,'E5-2_08', 'Description of contribution to circular economy');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (483,'E5-2_09', 'Description of other stakeholders involved in collective action (resource use and circular economy)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (484,'E5-2_10', 'Description of organisation of project (resource use and circular economy)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (485,'E5.MDR-A_13-14', 'Disclosures to be reported if the undertaking has not adopted actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (486,'E5.MDR-T_01-13', 'Tracking effectiveness of policies and actions through targets [see ESRS 2 MDR-T ]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (487,'E5-3_01', 'Disclosure of how target relates to resources (resource use and circular economy)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (488,'E5-3_02', 'Disclosure of how target relates to increase of circular design');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (489,'E5-3_03', 'Disclosure of how target relates to increase of circular material use rate');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (490,'E5-3_04', 'Disclosure of how target relates to minimisation of primary raw material');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (491,'E5-3_05', 'Disclosure of how target relates to reversal of depletion of stock of renewable resources');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (492,'E5-3_06', 'Target relates to waste management');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (493,'E5-3_07', 'Disclosure of how target relates to waste management');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (494,'E5-3_08', 'Disclosure of how target relates to other matters related to resource use or circular economy');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (495,'E5-3_09', 'Layer in waste hierarchy to which target relates');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (496,'E5-3_10', 'Disclosure of ecological threshold identified and methodology used to identify ecological threshold (resource use and circular economy)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (497,'E5-3_11', 'Disclosure of how ecological entity-specific threshold was determined (resource use and circular economy)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (498,'E5-3_12', 'Disclosure of how responsibility for respecting identified ecological threshold is allocated (resource use and circular economy)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (499,'E5-3_13', 'The targets being  set and presented are mandatory (required by legislation)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (500,'E5.MDR-T_14-19', 'Disclosures to be reported if the undertaking has not adopted targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (501,'E5-4_01', 'Disclosure of information on material resource inflows');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (502,'E5-4_02', 'Overall total weight of products and technical and biological materials used during the reporting period');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (503,'E5-4_03', 'Percentage of biological materials (and biofuels used for non-energy purposes)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (504,'E5-4_04', 'The absolute weight of secondary reused or recycled components, secondary intermediary products and secondary materials used to manufacture the undertaking’s products and services (including packaging)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (505,'E5-4_05', 'Percentage of secondary reused or recycled components, secondary intermediary products and secondary materials');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (506,'E5-4_06', 'Description of methodologies used to calculate data and key assumptions used');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (507,'E5-4_07', 'Description of materials that are sourced from by-products or waste stream');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (508,'E5-4_08', 'Description of how double counting was avoided and of choices made');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (509,'E5-5_01', 'Description of the key products and materials that come out of the undertaking’s production process');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (510,'E5-5_02', 'Disclosure of the expected durability of the products placed on the market, in relation to the industry average for each product group');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (511,'E5-5_03', 'Disclosure of the reparability of products');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (512,'E5-5_04', 'The rates of recyclable content in products');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (513,'E5-5_05', 'The rates of recyclable content in products packaging');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (514,'E5-5_06', 'Description of methodologies used to calculate data (resource outflows)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (515,'E5-5_07', 'Total Waste generated');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (516,'E5-5_08', 'Waste diverted from disposal, breakdown by hazardous and non-hazardous waste and treatment type');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (517,'E5-5_09', 'Waste directed to disposal, breakdown by hazardous and non-hazardous waste and treatment type');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (518,'E5-5_10', 'Non-recycled waste');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (519,'E5-5_11', 'Percentage of non-recycled waste');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (520,'E5-5_12', 'Disclosure of composition of waste');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (521,'E5-5_13', 'Disclosure of waste streams relevant to undertaking''s sector or activities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (522,'E5-5_14', 'Disclosure of materials that are present in waste');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (523,'E5-5_15', 'Total amount of hazardous waste');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (524,'E5-5_16', 'Total amount of radioactive waste');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (525,'E5-5_17', 'Description of methodologies used to calculate data (waste generated)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (526,'E5-5_18', 'Disclosure of its engagement in product end-of-life waste management');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (527,'E5-6_01', 'Disclosure of quantitative information about anticipated financial effects of material risks and opportunities arising from resource use and circular economy-related impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (528,'E5-6_02', 'Disclosure of qualitative information of anticipated financial effects of material risks and opportunities arising from resource use and circular economy-related impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (529,'E5-6_03', 'Description of effects considered and related impacts (resource use and circular economy)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (530,'E5-6_04', 'Disclosure of critical assumptions used in estimates of financial effects of material risks and opportunities arising from resource use and circular economy-related impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (531,'E5-6_05', 'Description of related products and services at risk (resource use and circular economy)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (532,'E5-6_06', 'Explanation of how time horizons are defined, financial amounts are estimated and of critical assumptions made (resource use and circular economy)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (533,'S1.SBM-3_01', 'All people in its own workforce who can be materially impacted by undertaking are included in scope of disclosure under ESRS 2');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (534,'S1.SBM-3_02', 'Description of types of employees and non-employees in its own workforce subject to material impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (535,'S1.SBM-3_03', 'Material negative impacts occurrence (own workforce)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (536,'S1.SBM-3_04', 'Description of activities that result in positive impacts and types of employees and non-employees in its own workforce that are positively affected or could be positively affected');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (537,'S1.SBM-3_05', 'Description of material risks and opportunities arising from impacts and dependencies on own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (538,'S1.SBM-3_06', 'Description of material impacts on workers that may arise from transition plans for reducing negative impacts on environment and achieving greener and climate-neutral operations');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (539,'S1.SBM-3_07', 'Information about type of operations at significant risk of incidents of forced labour or compulsory labour');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (540,'S1.SBM-3_08', 'Information about countries or geographic areas with operations considered at significant risk of incidents of forced labour or compulsory labour');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (541,'S1.SBM-3_09', 'Information about type of operations at significant risk of incidents of child labour');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (542,'S1.SBM-3_10', 'Information about countries or geographic areas with operations considered at significant risk of incidents of child labour');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (543,'S1.SBM-3_11', 'Disclosure of whether and how understanding of people in its own workforce with particular characteristics, working in particular contexts, or undertaking particular activities may be at greater risk of harm has been developed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (544,'S1.SBM-3_12', 'Disclosure of which of material risks and opportunities arising from impacts and dependencies on people in its own workforce  relate to specific groups of people');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (545,'S1.MDR-P_01-06', 'Policies to manage material impacts, risks and opportunities related to its own workforce [see ESRS 2 MDR-P]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (546,'S1-1_01', 'Policies to manage material impacts, risks and opportunities related to own workforce, including for specific groups within workforce or all own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (547,'S1-1_02', 'Disclosure of explanations of significant changes to policies adopted during reporting year');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (548,'S1-1_03', 'Description of relevant human rights policy commitments relevant to own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (549,'S1-1_04', 'Disclosure of general approach in relation to respect for human rights including labour rights, of people in its own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (550,'S1-1_05', 'Disclosure of general approach in relation to engagement with people in its own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (551,'S1-1_06', 'Disclosure of general approach in relation to measures to provide and (or) enable remedy for human rights impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (552,'S1-1_07', 'Disclosure of whether and how policies are aligned with relevant internationally recognised instruments');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (553,'S1-1_08', 'Policies explicitly address trafficking in human beings, forced labour or compulsory labour and child labour');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (554,'S1-1_09', 'Workplace accident prevention policy or management system is in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (555,'S1-1_10', 'Specific policies aimed at elimination of discrimination are in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (556,'S1-1_11', 'Grounds for discrimination are specifically covered in policy');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (557,'S1-1_12', 'Disclosure of specific policy commitments related to inclusion and (or) positive action for people from groups at particular risk of vulnerability in own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (558,'S1-1_13', 'Disclosure of whether and how policies are implemented through specific procedures to ensure discrimination is prevented, mitigated and acted upon once detected, as well as to advance diversity and inclusion');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (559,'S1-1_14', 'Disclosure on an illustration of the types of communication of its policies to those individuals, group of individuals or entities for whom they are relevant');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (560,'S1-1_15', 'Policies and procedures which make qualifications, skills and experience the basis for the recruitment, placement, training and advancement are in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (561,'S1-1_16', 'Has assigned responsibility at top management level for equal treatment and opportunities in employment, issue clear company-wide policies and procedures to guide equal employment practices, and link advancement to desired performance in this area');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (562,'S1-1_17', 'Staff training on non-discrimination policies and practices are in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (563,'S1-1_18', 'Adjustments to the physical environment to ensure health and safety for workers, customers and other visitors with disabilities are in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (564,'S1-1_19', 'Has evaluated whether a there is a risk that job requirements have been defined in a way that would systematically disadvantage certain groups');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (565,'S1-1_20', 'Keeping an up-to-date records on recruitment, training and promotion that provide a transparent view of opportunities for employees and their progression');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (566,'S1-1_21', 'Has put in place grievance procedures to address complaints, handle appeals and provide recourse for employees when discrimination is identified, and is alert to formal structures and informal cultural issues that can prevent employees from raising concerns and grievances');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (567,'S1-1_22', 'Has programs to promote access to skills development.');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (568,'S1.MDR-P_07-08', 'Disclosures to be reported in case the undertaking has not adopted policies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (569,'S1-2_01', 'Disclosure of whether and how perspectives of own workforce  inform decisions or activities aimed at managing actual and potential  impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (570,'S1-2_02', 'Engagement occurs with own workforce  or their representatives');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (571,'S1-2_03', 'Disclosure of stage at which engagement occurs, type of engagement and frequency of engagement');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (572,'S1-2_04', 'Disclosure of function and most senior role within undertaking that has operational responsibility for ensuring that engagement happens and that results inform undertakingâ€™s approach');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (573,'S1-2_05', 'Disclosure of Global Framework Agreement or other agreements related to respect of human rights of workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (574,'S1-2_06', 'Disclosure of how effectiveness of engagement with its own workforce  is assessed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (575,'S1-2_07', 'Disclosure of steps taken to gain insight into perspectives of people in its own workforce that may be particularly vulnerable to impacts and (or) marginalised');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (576,'S1-2_08', 'Statement in case the undertaking has not adopted a general process to engage with its own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (577,'S1-2_09', 'Disclosure of timeframe for adoption of general process to engage with its own workforce in case the undertaking has not adopted a general process for engagement');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (578,'S1-2_10', 'Disclosure of how undertaking engages with at-risk or persons in vulnerable situations');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (579,'S1-2_11', 'Disclosure of how potential barriers to engagement with people in its workforce are taken into account');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (580,'S1-2_12', 'Disclosure of how people in its workforce are provided with information that is understandable and accessible through appropriate communication channels');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (581,'S1-2_13', 'Disclosure of any conflicting interests that have arisen among different workers and how these conflicting interests have been resolved');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (582,'S1-2_14', 'Disclosure of how undertaking seeks to respect human rights of all stakeholders engaged');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (583,'S1-2_15', 'Information about effectiveness of processes for engaging with its own workforce from previous reporting periods');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (584,'S1-3_01', 'Disclosure of general approach to and processes for providing or contributing to remedy where undertaking has caused or contributed to a material negative impact on people in its own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (585,'S1-3_02', 'Disclosure of specific channels in place for its own workforce to raise concerns or needs directly with undertaking and have them addressed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (586,'S1-3_03', 'Third-party mechanisms are accessible to all own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (587,'S1-3_04', 'Disclosure of whether and how own workforce and their workers'' representatives are able to access channels at level of undertaking they are employed by or contracted to work for');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (588,'S1-3_05', 'Grievance or complaints handling mechanisms related to employee matters exist');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (589,'S1-3_06', 'Disclosure of processes through which undertaking supports or requires availability of channels');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (590,'S1-3_07', 'Disclosure of how issues raised and addressed are tracked and monitored and how effectiveness of channels is ensured');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (591,'S1-3_08', 'Disclosure of whether and how it is assessed that its own workforce is aware of and trust structures or processes as way to raise their concerns or needs and have them addressed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (592,'S1-3_09', 'Policies regarding protection against retaliation for individuals that use channels to raise concerns or needs are in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (593,'S1-3_10', 'Statement in case the undertaking has not adopted a channel for raising concerns');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (594,'S1-3_11', 'Disclosure of timeframe for channel for raising concerns to be in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (595,'S1.MDR-A_01-12', 'Action plans and resources to manage its material impacts, risks, and opportunities related to its own workforce [see ESRS 2 - MDR-A]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (596,'S1-4_01', 'Description of action taken, planned or underway to prevent or mitigate negative impacts on own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (597,'S1-4_02', 'Disclosure on whether and how action has been taken to provide or enable remedy in relation to actual material impact');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (598,'S1-4_03', 'Description of additional initiatives or actions with primary purpose of delivering positive impacts for own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (599,'S1-4_04', 'Description of how effectiveness of actions and initiatives in delivering outcomes for own workforce is tracked and assessed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (600,'S1-4_05', 'Description of process through which it identifies what action is needed and appropriate in response to particular actual or potential negative impact on own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (601,'S1-4_06', 'Description of what action is planned or underway to mitigate material risks arising from impacts and dependencies on own workforce  and how effectiveness is tracked');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (602,'S1-4_07', 'Description of what action is planned or underway to pursue material opportunities in relation to own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (603,'S1-4_08', 'Disclosure of whether and how it is ensured that own practices do not cause or contribute to material negative impacts on own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (604,'S1-4_09', 'Disclosure of resources are allocated to the management of material impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (605,'S1-4_10', 'Disclosure of general and specific approaches to addressing material negative impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (606,'S1-4_11', 'Disclosure of initiatives aimed at contributing to additional material positive impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (607,'S1-4_12', 'Disclosure of how far undertaking has progressed in efforts during reporting period');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (608,'S1-4_13', 'Disclosure of aims for continued improvement');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (609,'S1-4_14', 'Disclosure of whether and how undertaking seeks to use leverage with relevant business relationships to manage material negative impacts affecting own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (610,'S1-4_15', 'Disclosure of how the initiative, and its own involvement, is aiming to address the material impact concerned');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (611,'S1-4_16', 'Disclosure of whether and how workers and workers'' representatives play role in decisions regarding design and implementation of programmes or processes whose primary aim is to deliver positive impacts for workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (612,'S1-4_17', 'Information about intended or achieved positive outcomes of programmes or processes for own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (613,'S1-4_18', 'Initiatives or processes whose primary aim is to deliver positive impacts for own workforce  are designed also to support achievement of one or more of Sustainable Development Goals');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (614,'S1-4_19', 'Information about measures taken to mitigate negative impacts on workers that arise from transition to greener, climate-neutral economy');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (615,'S1-4_20', 'Description of internal functions that are involved in managing impacts and types of action taken by internal functions to address negative and advance positive impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (616,'S1.MDR-A_13-14', 'Disclosures to be reported if the undertaking has not adopted actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (617,'S1.MDR-T_01-13', 'Targets set to manage material impacts, risks and opportunities related to own workforce [see ESRS 2 - MDR-T]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (618,'S1-5_01', 'Disclosure of whether and how own workforce or workforce'' representatives were engaged directly in setting targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (619,'S1-5_02', 'Disclosure of whether and how own workforce or workforce'' representatives were engaged directly in tracking performance against targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (620,'S1-5_03', 'Disclosure of whether and how own workforce or workforce'' representatives were engaged directly in identifying lessons or improvements as result of undertakings performance');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (621,'S1-5_04', 'Disclosure of intended outcomes to be achieved in lives of people in its own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (622,'S1-5_05', 'Information about stability over time of target in terms of definitions and methodologies to enable comparability');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (623,'S1-5_06', 'Disclosure of references to standards or commitments which targets are based on');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (624,'S1.MDR-T_14-19', 'Disclosures to be reported if the undertaking has not adopted targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (625,'S1-6_01', 'Characteristics of undertaking''s employees - number of employees by gender [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (626,'S1-6_02', 'Number of employees (head count)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (627,'S1-6_03', 'Average number of employees (head count)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (628,'S1-6_04', 'Characteristics of undertaking''s employees - number of employees in countries with 50 or more employees representing at least 10% of total number of employees [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (629,'S1-6_05', 'Number of employees in countries with 50 or more employees representing at least 10% of total number of employees');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (630,'S1-6_06', 'Average number of employees in countries with 50 or more employees representing at least 10% of total number of employees');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (631,'S1-6_07', 'Characteristics of undertaking''s employees - information on employees by contract type and gender  [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (632,'S1-6_08', 'Characteristics of undertaking''s employees - information on employees by region [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (633,'S1-6_09', 'Number of employees (head count or full-time equivalent)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (634,'S1-6_10', 'Average number of employees (head count or full-time equivalent)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (635,'S1-6_11', 'Number of employee who have left undertaking');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (636,'S1-6_12', 'Percentage of employee turnover');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (637,'S1-6_13', 'Description of methodologies and assumptions used to compile data (employees)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (638,'S1-6_14', 'Employees numbers are reported in head count or full-time equivalent');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (639,'S1-6_15', 'Employees numbers are reported at end of reporting period/average/other methodology');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (640,'S1-6_16', 'Disclosure of contextual information necessary to understand data (employees)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (641,'S1-6_17', 'Disclosure of cross-reference of information reported under paragragph 50 (a) to most representative number in financial statements');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (642,'S1-6_18', 'Further detailed breakdown by gender and by region [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (643,'S1-6_19', 'Number of full-time employees by head count or full time equivalent');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (644,'S1-6_20', 'Number of part-time employees by head count or full time equivalent');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (645,'S1-7_01', 'Number of non-employees in own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (646,'S1-7_02', 'Number of non-employees in own workforce - self-employed people');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (647,'S1-7_03', 'Number of non-employees in own workforce - people provided by undertakings primarily engaged in employment activities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (648,'S1-7_04', 'Undertaking does not have non-employees in own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (649,'S1-7_05', 'Disclosure of the most common types of non-employees (for example, self-employed people, people provided by undertakings primarily engaged in employment activities, and other types relevant to the undertaking), their relationship with the undertaking, and the type of work that they perform.');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (650,'S1-7_06', 'Description of methodologies and assumptions used to compile data (non-employees)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (651,'S1-7_07', 'Non-employees numbers are reported in head count/full time equivalent');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (652,'S1-7_08', 'Non-employees numbers are reported at end of reporting period/average/other methodology');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (653,'S1-7_09', 'Disclosure of contextual information necessary to understand data (non-employee workers)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (654,'S1-7_10', 'Description of basis of preparation of non-employees estimated number');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (655,'S1-8_01', 'Percentage of total employees covered by collective bargaining agreements');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (656,'S1-8_02', 'Percentage of own employees covered by collective bargaining agreements are within coverage rate by country with significant employment (in the EEA)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (657,'S1-8_03', 'Percentage of own employees covered by collective bargaining agreements (outside EEA) by region');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (658,'S1-8_04', 'Working conditions and terms of employment for employees not covered by collective bargaining agreements are determined based on collective bargaining agreements that cover other employees or based on collective bargaining agreements from other undertakings');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (659,'S1-8_05', 'Description of extent to which working conditions and terms of employment of non-employees in own workforce are determined or influenced by collective bargaining agreements');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (660,'S1-8_06', 'Percentage of employees in country country with significant employment (in the EEA) covered by workers'' representatives');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (661,'S1-8_07', 'Disclosure of existence of any agreement with employees for representation by European Works Council (EWC), Societas Europaea (SE) Works Council, or Societas Cooperativa Europaea (SCE) Works Council');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (662,'S1-8_08', 'Own workforce in region (non-EEA) covered by collective bargaining and social dialogue agreements by coverage rate and by region');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (663,'S1-9_01', 'Gender distribution in number of employees (head count) at top management level');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (664,'S1-9_02', 'Gender distribution in percentage of employees at top management level');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (665,'S1-9_03', 'Distribution of employees (head count) under 30 years old');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (666,'S1-9_04', 'Distribution of employees (head count) between 30 and 50 years old');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (667,'S1-9_05', 'Distribution of employees (head count) over 50 years old');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (668,'S1-9_06', 'Disclosure of own definition of top management used');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (669,'S1-10_01', 'All employees are paid adequate wage, in line with applicable benchmarks');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (670,'S1-10_02', 'Countries where employees earn below the applicable adequate wage benchmark [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (671,'S1-10_03', 'Percentage of  employees paid below the applicable adequate wage benchmark');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (672,'S1-10_04', 'Percentage of non-employees paid below adequate wage');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (673,'S1-11_01', 'All employees in own workforce are covered by social protection, through public programs or through benefits offered, against loss of income due to sickness');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (674,'S1-11_02', 'All employees in own workforce are covered by social protection, through public programs or through benefits offered, against loss of income due to unemployment starting from when own worker is working for undertaking');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (675,'S1-11_03', 'All employees in own workforce are covered by social protection, through public programs or through benefits offered, against loss of income due to employment injury and acquired disability');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (676,'S1-11_04', 'All employees in own workforce are covered by social protection, through public programs or through benefits offered, against loss of income due to parental leave');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (677,'S1-11_05', 'All employees in own workforce are covered by social protection, through public programs or through benefits offered, against loss of income due to retirement');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (678,'S1-11_06', 'Social protection employees by country [table] by types of events and type of employees [including non employees]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (679,'S1-11_07', 'Disclosure of types of employees who are not covered by social protection, through public programs or through benefits offered, against loss of income due to sickness');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (680,'S1-11_08', 'Disclosure of types of employees who are not covered by social protection, through public programs or through benefits offered, against loss of income due to unemployment starting from when own worker is working for undertaking');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (681,'S1-11_09', 'Disclosure of types of employees who are not covered by social protection, through public programs or through benefits offered, against loss of income due to employment injury and acquired disability');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (682,'S1-11_10', 'Disclosure of types of employees who are not covered by social protection, through public programs or through benefits offered, against loss of income due to maternity leave');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (683,'S1-11_11', 'Disclosure of types of employees who are not covered by social protection, through public programs or through benefits offered, against loss of income due to retirement');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (684,'S1-12_01', 'Percentage of persons with disabilities amongst employees, subject to legal restrictions on collection of data');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (685,'S1-12_02', 'Percentage of employees with disabilities in own workforce breakdown by gender [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (686,'S1-12_03', 'Disclosure of contextual information necessary to understand data and how data has been compiled (persons with disabilities))');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (687,'S1-13_01', 'Training and skills development indicators gender [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (688,'S1-13_02', 'Percentage of employees that participated in regular performance and career development reviews');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (689,'S1-13_03', 'Average number of training hours  by gender [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (690,'S1-13_04', 'Average number of training hours per person for employees');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (691,'S1-13_05', 'Percentage of employees that participated in regular performance and career development reviews by employee category [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (692,'S1-13_06', 'Average number of employees that participated in regular performance and career development reviews by employee category');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (693,'S1-13_07', 'Percentage of non-employees that participated in regular performance and career development reviews');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (694,'S1-14_01', 'Percentage of people in its own workforce who are covered by health and safety management system based on legal requirements and (or) recognised standards or guidelines');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (695,'S1-14_02', 'Number of fatalities in own workforce as result of work-related injuries and work-related ill health');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (696,'S1-14_03', 'Number of fatalities as result of work-related injuries and work-related ill health of other workers working on undertaking''s sites');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (697,'S1-14_04', 'Number of recordable work-related accidents for own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (698,'S1-14_05', 'Rate of recordable work-related accidents for own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (699,'S1-14_06', 'Number of cases of recordable work-related ill health of employees');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (700,'S1-14_07', 'Number of days lost to work-related injuries and fatalities from work-related accidents, work-related ill health and fatalities from ill health related to employees');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (701,'S1-14_08', 'Number of cases of recordable work-related ill health of non-employees');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (702,'S1-14_09', 'Number of days lost to work-related injuries and fatalities from work-related accidents, work-related ill health and fatalities from ill health realted to non-employees');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (703,'S1-14_10', 'Percentage of own workforce who are covered by health and safety management system based on legal requirements and (or) recognised standards or guidelines and which has been internally audited and (or) audited or certified by external party');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (704,'S1-14_11', 'Description of underlying standards for internal audit or external certification of health and safety management system');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (705,'S1-14_12', 'Number of cases of recordable work-related ill health detected among former own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (706,'S1-15_01', 'Percentage of employees entitled to take family-related leave');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (707,'S1-15_02', 'Percentage of entitled employees that took family-related leave');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (708,'S1-15_03', 'Percentage of entitled employees that took family-related leave by gender [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (709,'S1-15_04', 'All employees are entitled to family-related leaves through social policy and (or) collective bargaining agreements');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (710,'S1-16_01', 'Gender pay gap');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (711,'S1-16_02', 'Annual total remuneration ratio');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (712,'S1-16_03', 'Disclosure of contextual information necessary to understand data, how data has been compiled and other changes to underlying data that are to be considered');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (713,'S1-16_04', 'Gender pay gap breakdown by employee category and/or country/segment [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (714,'S1-16_05', 'Gender pay gap breakdown by employee category and ordinary basic salary and complementary/variable components');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (715,'S1-16_06', 'Remuneration ratio adjusted for purchasing power differences between countries');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (716,'S1-16_07', 'Description of methodology used for calculation of remuneration ratio adjusted for purchasing power differences between countries');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (717,'S1-17_01', 'Number of incidents of discrimination [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (718,'S1-17_02', 'Number of incidents of discrimination');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (719,'S1-17_03', 'Number of complaints filed through channels for people in own workforce to raise concerns');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (720,'S1-17_04', 'Number of complaints filed to National Contact Points for OECD Multinational Enterprises');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (721,'S1-17_05', 'Amount of fines, penalties, and compensation for damages as result of incidents of discrimination, including harassment and complaints filed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (722,'S1-17_06', 'Information about reconciliation of fines, penalties, and compensation for damages as result of violations regarding swork-related discrimination and harassment  with most relevant amount presented in financial statements');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (723,'S1-17_07', 'Disclosure of contextual information necessary to understand data and how data has been compiled (work-related grievances, incidents and complaints related to social and human rights matters)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (724,'S1-17_08', 'Number of severe human rights issues and incidents connected to own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (725,'S1-17_09', 'Number of severe human rights issues and incidents connected to own workforce that are cases of non respect of UN Guiding Principles and OECD Guidelines for Multinational Enterprises');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (726,'S1-17_10', 'No severe human rights issues and incidents connected to own workforce have occurred');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (727,'S1-17_11', 'Amount of fines, penalties, and compensation for severe human rights issues and incidents connected to own workforce');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (728,'S1-17_12', 'Information about reconciliation of amount of fines, penalties, and compensation for severe human rights issues and incidents connected to own workforce with most relevant amount presented in financial statements');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (729,'S1-17_13', 'Disclosure of the status of incidents and/or complaints and actions taken');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (730,'S1-17_14', 'Number of severe human rights cases where undertaking played role securing remedy for those affected');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (731,'S2.SBM-3_01', 'All value chain workers  who can be materially impacted by undertaking are included in scope of disclosure under ESRS 2');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (732,'S2.SBM-3_02', 'Description of types of value chain workers subject to material impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (733,'S2.SBM-3_03', 'Type of value chain workers subject to material impacts by own operations or through value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (734,'S2.SBM-3_04', 'Disclosure of geographies or commodities for which there is significant risk of child labour, or of forced or compulsory labour, among workers in undertaking’s value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (735,'S2.SBM-3_05', 'Material negative impacts occurrence (value chain workers)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (736,'S2.SBM-3_06', 'Description of activities that result in positive impacts and types of value chain workers  that are positively affected or could be positively affected');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (737,'S2.SBM-3_07', 'Description of material risks and opportunities arising from impacts and dependencies on  value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (738,'S2.SBM-3_08', 'Disclosure of whether and how the undertaking has developed an understanding of how workers with particular characteristics, those working in particular contexts, or those undertaking particular activities may be at greater risk of harm.');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (739,'S2.SBM-3_09', 'Disclosure of which of material risks and opportunities arising from impacts and dependencies on value chain workers  are impacts on specific groups');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (740,'S2.MDR-P_01-06', 'Policies to manage material impacts, risks and opportunities related to value chain workers [see ESRS 2 MDR-P]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (741,'S2-1_01', 'Description of relevant human rights policy commitments relevant to value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (742,'S2-1_02', 'Disclosure of general approach in relation to respect for human rights relevant to value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (743,'S2-1_03', 'Disclosure of general approach in relation to engagement with value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (744,'S2-1_04', 'Disclosure of general approach in relation to measures to provide and (or) enable remedy for human rights impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (745,'S2-1_05', 'Policies explicitly address trafficking in human beings, forced labour or compulsory labour and child labour');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (746,'S2-1_06', 'Undertaking has supplier code of conduct');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (747,'S2-1_07', 'Provisions in supplier codes of conudct are fully in line with applicable ILO standards');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (748,'S2-1_08', 'Disclosure of whether and how policies are aligned with relevant internationally recognised instruments');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (749,'S2-1_09', 'Disclosure of extent and indication of nature of cases of non-respect of the UN Guiding Principles on Business and Human Rights, ILO Declaration on Fundamental Principles and Rights at Work or OECD Guidelines for Multinational Enterprises that involve value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (750,'S2-1_10', 'Disclosure of explanations of significant changes to policies adopted during reporting year');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (751,'S2-1_11', 'Disclosure on an illustration of the types of communication of its policies to those individuals, group of individuals or entities for whom they are relevant');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (752,'S2.MDR-P_07-08', 'Disclosures to be reported in case the undertaking has not adopted policies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (753,'S2-2_01', 'Disclosure of whether and how perspectives of value chain workers inform decisions or activities aimed at managing actual and potential  impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (754,'S2-2_02', 'Engagement occurs with value chain workers or their legitimate representatives directly, or with credible proxies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (755,'S2-2_03', 'Disclosure of stage at which engagement occurs, type of engagement and frequency of engagement');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (756,'S2-2_04', 'Disclosure of function and most senior role within undertaking that has operational responsibility for ensuring that engagement happens and that results inform undertakings approach');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (757,'S2-2_05', 'Disclosure of Global Framework Agreement or other agreements related to respect of human rights of workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (758,'S2-2_06', 'Disclosure of how effectiveness of engagement with value chain workers is assessed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (759,'S2-2_07', 'Disclosure of steps taken to gain insight into perspectives of value chain workers that may be particularly vulnerable to impacts and (or) marginalised');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (760,'S2-2_08', 'Statement in case the undertaking has not adopted a general process to engage with value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (761,'S2-2_09', 'Disclosure of timeframe for adoption of general process to engage with  value chain workers  in case the undertaking has not adopted a general process for engagement');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (762,'S2-3_01', 'Disclosure of general approach to and processes for providing or contributing to remedy where undertaking has identified that it connected with a material negative impact on  value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (763,'S2-3_02', 'Disclosure of specific channels in place for value chain workers to raise concerns or needs directly with undertaking and have them addressed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (764,'S2-3_03', 'Disclosure of processes through which undertaking supports or requires availability of channels');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (765,'S2-3_04', 'Disclosure of how issues raised and addressed are tracked and monitored and how effectiveness of channels is ensured');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (766,'S2-3_05', 'Disclosure of whether and how it is assessed that value chain workers are aware of and trust structures or processes as way to raise their concerns or needs and have them addressed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (767,'S2-3_06', 'Policies regarding protection against retaliation for individuals that use channels to raise concerns or needs are in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (768,'S2-3_07', 'Statement in case the undertaking has not adopted a channel for raising concerns');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (769,'S2-3_08', 'Disclosure of timeframe for channel for raising concerns to be in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (770,'S2-3_09', 'Disclosure of whether and how value chain workers are able to access channels at level of undertaking they are employed by or contracted to work for');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (771,'S2-3_10', 'Third-party mechanisms are accessible to all workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (772,'S2-3_11', 'Grievances are treated confidentially and with respect to rights of privacy and data protection');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (773,'S2-3_12', 'Channels to raise concerns or needs allow for value chain workers to use them anonymously');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (774,'S2.MDR-A_01-12', 'Action plans and resources to manage its material impacts, risks, and opportunities related to value chain workers [see ESRS 2 - MDR-A]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (775,'S2-4_01', 'Description of action planned or underway to prevent, mitigate or remediate material negative impacts on  value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (776,'S2-4_02', 'Description of whether and how action to provide or enable remedy in relation to an actual material impact');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (777,'S2-4_03', 'Description of additional initiatives or processes with primary purpose of delivering positive impacts for value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (778,'S2-4_04', 'Description of how effectiveness of actions or initiatives in delivering outcomes for value chain workersis tracked and assessed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (779,'S2-4_05', 'Description of processes to identifying what action is needed and appropriate in response to particular actual or potential material negative impact on  value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (780,'S2-4_06', 'Description of approach to taking action in relation to specific material negative impacts on value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (781,'S2-4_07', 'Description of approach to ensuring that processes to provide or enable remedy in event of material negative impacts on  value chain workers  are available and effective in their implementation and outcomes');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (782,'S2-4_08', 'Description of what action is planned or underway to mitigate material risks arising from impacts and dependencies on  value chain workers  and how effectiveness is tracked');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (783,'S2-4_09', 'Description of what action is planned or underway to pursue material opportunities in relation to  value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (784,'S2-4_10', 'Disclosure of whether and how it is ensured that own practices do not cause or contribute to material negative impacts on value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (785,'S2-4_11', 'Disclosure of severe human rights issues and incidents connected to upstream and downstream value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (786,'S2-4_12', 'Disclosure of resources allocated to management of material impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (787,'S2-4_13', 'Disclosure of whether and how undertaking seeks to use leverage with relevant business relationships to manage material negative impacts affecting value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (788,'S2-4_14', 'Disclosure of how participation in industry or multi-stakeholder initiative and undertaking''s own involvement is aiming to address material impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (789,'S2-4_15', 'Disclosure of whether and how value chain workers and legitimate representatives or their credible proxies play role in decisions regarding design and implementation of programmes or processes');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (790,'S2-4_16', 'Information about intended or achieved positive outcomes of programmes or processes for  value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (791,'S2-4_17', 'Initiatives or processes whose primary aim is to deliver positive impacts for  value chain workers  are designed also to support achievement of one or more of Sustainable Development Goals');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (792,'S2-4_18', 'Description of internal functions that are involved in managing impacts and types of action taken by internal functions to address negative and advance positive impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (793,'S2.MDR-A_13-14', 'Disclosures to be reported if the undertaking has not adopted actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (794,'S2.MDR-T_01-13', 'Targets set to manage material impacts, risks and opportunities related to value chain workers [see ESRS 2 - MDR-T]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (795,'S2-5_01', 'Disclosure of whether and how value chain workers , their legitimate representatives or credible proxies were engaged directly in setting targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (796,'S2-5_02', 'Disclosure of whether and how value chain workers , their legitimate representatives or credible proxies were engaged directly in tracking performance against targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (797,'S2-5_03', 'Disclosure of whether and how value chain workers , their legitimate representatives or credible proxies were engaged directly in identifying lessons or improvements as result of undertaking’s performance');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (798,'S2-5_04', 'Disclosure of intended outcomes to be achieved in lives of  value chain workers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (799,'S2-5_05', 'Information about stability over time of target in terms of definitions and methodologies to enable comparability');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (800,'S2-5_06', 'Disclosure of references to standards or commitments on which target is based');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (801,'S2.MDR-T_14-19', 'Disclosures to be reported if the undertaking has not adopted targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (802,'S3.SBM-3_01', 'All  affected communities who can be materially impacted by undertaking are included in scope of disclosure under ESRS 2');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (803,'S3.SBM-3_02', 'Description of types of affected communities subject to material impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (804,'S3.SBM-3_03', 'Type of communities subject to material impacts by own operations or through value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (805,'S3.SBM-3_04', 'Material negative impacts occurrence (affected communities)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (806,'S3.SBM-3_05', 'Description of activities that result in positive impacts and types of affected communities that are positively affected or could be positively affected');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (807,'S3.SBM-3_06', 'Description of material risks and opportunities arising from impacts and dependencies on  affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (808,'S3.SBM-3_07', 'Disclosure of whether and how the undertaking has developed an understanding of how affected communities with particular characteristics or those living in particular contexts, or those undertaking particular activities may be at greater risk of harm');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (809,'S3.SBM-3_08', 'Disclosure of which of material risks and opportunities arising from impacts and dependencies on affected communities are impacts on specific groups');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (810,'S3.MDR-P_01-06', 'Policies to manage material impacts, risks and opportunities related to affected communities [see ESRS 2 MDR-P]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (811,'S3-1_01', 'Disclosure of any  any particular policy provisions for preventing and addressing impacts on indigenous peoples');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (812,'S3-1_02', 'Description of relevant human rights policy commitments relevant to affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (813,'S3-1_03', 'Disclosure of general approach in relation to respect for human rights of communities, and indigenous peoples specifically');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (814,'S3-1_04', 'Disclosure of general approach in relation to engagement with affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (815,'S3-1_05', 'Disclosure of general approach in relation to measures to provide and (or) enable remedy for human rights impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (816,'S3-1_06', 'Disclosure of whether and how policies are aligned with relevant internationally recognised instruments');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (817,'S3-1_07', 'Disclosure of extent and indication of nature of cases of non-respect of the UN Guiding Principles on Business and Human Rights, ILO Declaration on Fundamental Principles and Rights at Work or OECD Guidelines for Multinational Enterprises that involve affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (818,'S3-1_08', 'Disclosure of explanations of significant changes to policies adopted during reporting year');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (819,'S3-1_09', 'Disclosure on an illustration of the types of communication of its policies to those individuals, group of individuals or entities for whom they are relevant');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (820,'S3.MDR-P_07-08', 'Disclosures to be reported in case the undertaking has not adopted policies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (821,'S3-2_01', 'Disclosure of whether and how perspectives of affected communities inform decisions or activities aimed at managing actual and potential impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (822,'S3-2_02', 'Engagement occurs with affected communities or their legitimate representatives directly, or with credible proxies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (823,'S3-2_03', 'Disclosure of stage at which engagement occurs, type of engagement and frequency of engagement');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (824,'S3-2_04', 'Disclosure of function and most senior role within undertaking that has operational responsibility for ensuring that engagement happens and that results inform undertakings approach');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (825,'S3-2_05', 'Disclosure of how the undertaking assesses the effectiveness of its engagement with affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (826,'S3-2_06', 'Disclosure of steps taken to gain insight into perspectives of  affected communities hat may be particularly vulnerable to impacts and (or) marginalised');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (827,'S3-2_07', 'Disclosure of whether and how the undertaking takes into account and ensures respect of particular rights of indigenous peoples in its stakeholder engagement approach');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (828,'S3-2_08', 'Statement in case the undertaking has not adopted a general process to engage with affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (829,'S3-2_09', 'Disclosure of timeframe for adoption of general process to engage with affected communities in case the undertaking has not adopted a general process for engagement');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (830,'S3-3_10', 'Disclosure of general approach to and processes for providing or contributing to remedy where undertaking has identified that it connected with a material negative impact on affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (831,'S3-3_11', 'Disclosure of specific channels in place for affected communities to raise concerns or needs directly with undertaking and have them addressed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (832,'S3-3_12', 'Disclosure of processes through which undertaking supports or requires availability of channels');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (833,'S3-3_13', 'Disclosure of how issues raised and addressed are tracked and monitored and how effectiveness of channels is ensured');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (834,'S3-3_14', 'Disclosure of whether and how it is assessed that affected communities are aware of and trust structures or processes as way to raise their concerns or needs and have them addressed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (835,'S3-3_15', 'Policies regarding protection against retaliation for individuals that use channels to raise concerns or needs are in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (836,'S3-3_16', 'Statement in case the undertaking has not adopted a general process to engage with affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (837,'S3-3_17', 'Disclosure of timeframe for channel or processes for raising concerns to be in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (838,'S3-3_18', 'Disclosure of whether and how affected communities are able to access channels at level of undertaking they are affected by');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (839,'S3-3_19', 'Third-party mechanisms are accessible to all affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (840,'S3-3_20', 'Grievances are treated confidentially and with respect to rights of privacy and data protection');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (841,'S3-3_21', 'affected communities   are allowed to use anonymously channels to raise concerns or needs');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (842,'S3.MDR-A_01-12', 'Action plans and resources to manage its material impacts, risks, and opportunities related to affected communities [see ESRS 2 - MDR-A]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (843,'S3-4_01', 'Description of action taken, planned or underway to prevent, mitigate or remediate material negative impacts on affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (844,'S3-4_02', 'Description of whether and how the undertaking has taken action to provide or enable remedy in relation to an actual material impact');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (845,'S3-4_03', 'Description of additional initiatives or processes with primary purpose of delivering positive impacts for affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (846,'S3-4_04', 'Description of how effectiveness of actions or initiatives in delivering outcomes for affected communities is tracked and assessed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (847,'S3-4_05', 'Description of processes to identifying what action is needed and appropriate in response to particular actual or potential material negative impact on affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (848,'S3-4_06', 'Description of approach to taking action in relation to specific material negative impacts on affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (849,'S3-4_07', 'Description of approach to ensuring that processes to provide or enable remedy in event of material negative impacts on affected communities are available and effective in their implementation and outcomes');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (850,'S3-4_08', 'Description of what action is planned or underway to mitigate material risks arising from impacts and dependencies on affected communities and how effectiveness is tracked');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (851,'S3-4_09', 'Description of what action is planned or underway to pursue material opportunities in relation to affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (852,'S3-4_10', 'Disclosure of whether and how it is ensured that own practices do not cause or contribute to material negative impacts on affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (853,'S3-4_11', 'Disclosure of severe human rights issues and incidents connected to affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (854,'S3-4_12', 'Disclosure of resources allocated to management of material impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (855,'S3-4_13', 'Disclosure of whether and how undertaking seeks to use leverage with relevant business relationships to manage material negative impacts affecting  affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (856,'S3-4_14', 'Disclosure of how participation in industry or multi-stakeholder initiative and undertaking''s own involvement is aiming to address material impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (857,'S3-4_15', 'Disclosure of whether and how affected communities  play role in decisions regarding design and implementation of programmes or investments');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (858,'S3-4_16', 'Information about intended or achieved positive outcomes of programmes or investments for  affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (859,'S3-4_17', 'Explanation of the approximate scope of affected communities covered by the described social investment or development programmes, and, where applicable, the rationale for why selected communities were chosen');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (860,'S3-4_18', 'Initiatives or processes whose primary aim is to deliver positive impacts for affected communities are designed also to support achievement of one or more of Sustainable Development Goals');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (861,'S3-4_19', 'Description of internal functions that are involved in managing impacts and types of action taken by internal functions to address negative and advance positive impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (862,'S3.MDR-A_13-14', 'Disclosures to be reported if the undertaking has not adopted actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (863,'S3.MDR-T_01-13', 'Targets set to manage material impacts, risks and opportunities related to affected communities [see ESRS 2 - MDR-T]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (864,'S3-5_01', 'Disclosure of whether and how affected communities were engaged directly in setting targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (865,'S3-5_02', 'Disclosure of whether and how affected communities  were engaged directly in tracking performance against targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (866,'S3-5_03', 'Disclosure of whether and how affected communities  were engaged directly in identifying lessons or improvements as result of undertaking’s performance');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (867,'S3-5_04', 'Disclosure of intended outcomes to be achieved in lives of  affected communities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (868,'S3-5_05', 'Information about stability over time of target in terms of definitions and methodologies to enable comparability');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (869,'S3-5_06', 'Disclosure of references to standards or commitments on which target is based');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (870,'S3.MDR-T_14-19', 'Disclosures to be reported if the undertaking has not adopted targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (871,'S4.SBM-3_01', 'All  consumers and end-users who can be materially impacted by undertaking are included in scope of disclosure under ESRS 2');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (872,'S4.SBM-3_02', 'Description of types of consumers and end-users subject to material impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (873,'S4.SBM-3_03', 'Type of consumers and end-users subject to material impacts by own operations or through value chain');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (874,'S4.SBM-3_04', 'Material negative impacts occurrence (consumers and end-users)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (875,'S4.SBM-3_05', 'Description of activities that result in positive impacts and types of  consumers and end-users that are positively affected or could be positively affected');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (876,'S4.SBM-3_06', 'Description of material risks and opportunities arising from impacts and dependencies on  consumers and end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (877,'S4.SBM-3_07', 'Disclosure of whether and how understanding of how consumers and end-users with particular characteristics, working in particular contexts, or undertaking particular activities may be at greater risk of harm has been developed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (878,'S4.SBM-3_08', 'Disclosure of which of material risks and opportunities arising from impacts and dependencies on consumers and end-users are impacts on specific groups');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (879,'S4.MDR-P_01-06', 'Policies to manage material impacts, risks and opportunities related to consumers and end-users [see ESRS 2 MDR-P]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (880,'S4-1_01', 'Policies to manage material impacts, risks and opportunities related to affected consumers and end-users, including specific groups or all consumers / end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (881,'S4-1_02', 'Description of relevant human rights policy commitments relevant to consumers and/or end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (882,'S4-1_03', 'Disclosure of general approach in relation to respect for human rights of consumers and end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (883,'S4-1_04', 'Disclosure of general approach in relation to engagement with consumers and/or end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (884,'S4-1_05', 'Disclosure of general approach in relation to measures to provide and (or) enable remedy for human rights impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (885,'S4-1_06', 'Description of whether and how policies are aligned with relevant internationally recognised instruments');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (886,'S4-1_07', 'Disclosure of extent and indication of nature of cases of non-respect of the UN Guiding Principles on Business and Human Rights, ILO Declaration on Fundamental Principles and Rights at Work or OECD Guidelines for Multinational Enterprises that involve consumers and/or end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (887,'S4-1_08', 'Disclosure of explanations of significant changes to policies adopted during reporting year');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (888,'S4-1_09', 'Disclosure on an illustration of the types of communication of its policies to those individuals, group of individuals or entities for whom they are relevant');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (889,'S4.MDR-P_07-08', 'Disclosures to be reported in case the undertaking has not adopted policies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (890,'S4-2_01', 'Disclosure of whether and how perspectives of consumers and end-users inform decisions or activities aimed at managing actual and potential impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (891,'S4-2_02', 'Engagement occurs with consumers and end-users  or their legitimate representatives directly, or with credible proxies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (892,'S4-2_03', 'Disclosure of stage at which engagement occurs, type of engagement and frequency of engagement');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (893,'S4-2_04', 'Disclosure of function and most senior role within undertaking that has operational responsibility for ensuring that engagement happens and that results inform undertakings approach');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (894,'S4-2_05', 'Disclosure of how effectiveness of engagement with  consumers and end-users   is assessed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (895,'S4-2_06', 'Disclosure of steps taken to gain insight into perspectives of  consumers and end-users  / consumers and end-users that may be particularly vulnerable to impacts and (or) marginalised');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (896,'S4-2_07', 'Statement in case the undertaking has not adopted a general process to engage with consumers and/or end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (897,'S4-2_08', 'Disclosure of timeframe for adoption of general process to engage with  consumers and end-users   in case the undertaking has not adopted a general process for engageme');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (898,'S4-2_09', 'Type of role or function handling with engagement');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (899,'S4-3_01', 'Disclosure of general approach to and processes for providing or contributing to remedy where undertaking has identified that it connected with a material negative impact on consumers and end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (900,'S4-3_02', 'Disclosure of specific channels in place for consumers and end-users to raise concerns or needs directly with undertaking and have them addressed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (901,'S4-3_03', 'Disclosure of processes through which undertaking supports or requires availability of channels');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (902,'S4-3_04', 'Disclosure of how issues raised and addressed are tracked and monitored and how effectiveness of channels is ensured');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (903,'S4-3_05', 'Disclosure of whether and how it is assessed that consumers and end-users are aware of and trust structures or processes as way to raise their concerns or needs and have them addressed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (904,'S4-3_06', 'Policies regarding protection against retaliation for individuals that use channels to raise concerns or needs are in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (905,'S4-3_07', 'Statement in case the undertaking has not adopted a general process to engage with consumers and/or end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (906,'S4-3_08', 'Disclosure of timeframe for channel or processes for raising concerns to be in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (907,'S4-3_09', 'Disclosure of whether and how consumers and/or end-users are able to access channels at level of undertaking they are affected by');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (908,'S4-3_10', 'Third-party mechanisms are accessible to all consumers and/or end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (909,'S4-3_11', 'Grievances are treated confidentially and with respect to rights of privacy and data protection');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (910,'S4-3_12', 'consumers and end-users   are allowed to use anonymously channels to raise concerns or needs');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (911,'S4-3_13', 'Number of complaints received from consumers and/or end users during the reporting period');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (912,'S4.MDR-A_01-12', 'Action plans and resources to manage its material impacts, risks, and opportunities related to consumers and end-users [see ESRS 2 - MDR-A]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (913,'S4-4_01', 'Description of action planned or underway to prevent, mitigate or remediate material negative impacts on consumers and end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (914,'S4-4_02', 'Description of whether and how action has been taken to provide or enable remedy in relation to an actual material impact');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (915,'S4-4_03', 'Description of additional initiatives or processes with primary purpose of delivering positive impacts for consumers and end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (916,'S4-4_04', 'Description of how effectiveness of actions or initiatives in delivering outcomes for consumers and end-users is tracked and assessed');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (917,'S4-4_05', 'Description of approach to identifying what action is needed and appropriate in response to particular actual or potential material negative impact on  consumers and end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (918,'S4-4_06', 'Description of approach to taking action in relation to specific material impacts on consumers and end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (919,'S4-4_07', 'Description of approach to ensuring that processes to provide or enable remedy in event of material negative impacts on  consumers and end-users are available and effective in their implementation and outcomes');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (920,'S4-4_08', 'Description of what action is planned or underway to mitigate material risks arising from impacts and dependencies on consumers and end-users and how effectiveness is tracked');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (921,'S4-4_09', 'Description of what action is planned or underway to pursue material opportunities in relation to consumers and end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (922,'S4-4_10', 'Disclosure of whether and how it is ensured that own practices do not cause or contribute to material negative impacts on consumers and end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (923,'S4-4_11', 'Disclosure of severe human rights issues and incidents connected to consumers and/or end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (924,'S4-4_12', 'Disclosure of resources allocated to management of material impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (925,'S4-4_13', 'Disclosure of whether and how undertaking seeks to use leverage with relevant business relationships to manage material negative impacts affecting  consumers and end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (926,'S4-4_14', 'Disclosure of how participation in industry or multi-stakeholder initiative and undertaking''s own involvement is aiming to address material impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (927,'S4-4_15', 'Disclosure of how consumers and end-users play role in decisions regarding design and implementation of programmes or processes');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (928,'S4-4_16', 'Information about intended or achieved positive outcomes of programmes or processes for consumers and end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (929,'S4-4_17', 'Initiatives or processes whose primary aim is to deliver positive impacts for consumers and/or end-users are designed also to support achievement of one or more of Sustainable Development Goals');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (930,'S4-4_18', 'Description of internal functions that are involved in managing impacts and types of action taken by internal functions to address negative and advance positive impacts');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (931,'S4.MDR-A_13-14', 'Disclosures to be reported if the undertaking has not adopted actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (932,'S4.MDR-T_01-13', 'Targets set to manage material impacts, risks and opportunities related to consumers and end-users [see ESRS 2 - MDR-T]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (933,'S4-5_01', 'Disclosure of whether and how consumers and end-users were engaged directly in setting targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (934,'S4-5_02', 'Disclosure of whether and how consumers and end-users were engaged directly in tracking performance against targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (935,'S4-5_03', 'Disclosure of whether and how consumers and end-users were engaged directly in identifying lessons or improvements as result of undertaking’s performance');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (936,'S4-5_04', 'Disclosure of intended outcomes to be achieved in lives of consumers and end-users');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (937,'S4-5_05', 'Information about stability over time of target in terms of definitions and methodologies to enable comparability');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (938,'S4-5_06', 'Disclosure of references to standards or commitments on which target is based');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (939,'S4.MDR-T_14-19', 'Disclosures to be reported if the undertaking has not adopted targets');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (940,'G1.GOV-1_01', 'Disclosure of role of administrative, management and supervisory bodies related to business conduct');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (941,'G1.GOV-1_02', 'Disclosure of expertise of administrative, management and supervisory bodies on business conduct matters');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (942,'G1.MDR-P_01-06', 'Policies in place to manage its material impacts, risks and opportunities related to business conduct and corporate culture [see ESRS 2 MDR-P]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (943,'G1-1_01', 'Description of how the undertaking establishes, develops, promotes and evaluates its corporate culture');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (944,'G1-1_02', 'Description of the mechanisms for identifying, reporting and investigating concerns about unlawful behaviour or behaviour in contradiction of its code of conduct or similar internal rules');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (945,'G1-1_03', 'No policies on anti-corruption or anti-bribery consistent with United Nations Convention against Corruption are in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (946,'G1-1_04', 'Timetable for implementation of policies on anti-corruption or anti-bribery consistent with United Nations Convention against Corruption');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (947,'G1-1_05', 'Disclosure of safeguards for reporting irregularities including whistleblowing protection');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (948,'G1-1_06', 'No policies on protection of whistle-blowers are in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (949,'G1-1_07', 'Timetable for implementation of policies on protection of whistle-blowers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (950,'G1-1_08', 'Undertaking is committed to investigate business conduct incidents promptly, independently and objectively');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (951,'G1-1_09', 'Policies with respect to animal welfare are in place');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (952,'G1-1_10', 'Information about policy for training within organisation on business conduct');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (953,'G1-1_11', 'Disclosure of the functions that are most at risk in respect of corruption and bribery');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (954,'G1-1_12', 'Entity is subject to legal requirements with regard to protection of whistleblowers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (955,'G1-2_01', 'Description of policy to prevent late payments, especially to SMEs');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (956,'G1-2_02', 'Description of approaches in regard to relationships with suppliers, taking account risks related to supply chain and impacts on sustainability matters');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (957,'G1-2_03', 'Disclosure of whether and how social and environmental criteria are taken into account for selection of supply-side contractual partners');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (958,'G1.MDR-P_07-08', 'Disclosures to be reported in case the undertaking has not adopted policies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (959,'G1-3_01', 'Information about procedures in place to prevent, detect, and address allegations or incidents of corruption or bribery');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (960,'G1-3_02', 'Investigators or investigating committee are separate from chain of management involved in prevention and detection of corruption or bribery');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (961,'G1-3_03', 'Information about process to report outcomes to administrative, management and supervisory bodies');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (962,'G1-3_04', 'Disclosure of plans to adopt procedures to prevent, detect, and address allegations or incidents of corruption or bribery in case of no procedure');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (963,'G1-3_05', 'Information about how policies are communicated to those for whom they are relevant (prevention and detection of corruption or bribery)');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (964,'G1-3_06', 'Information about nature, scope and depth of anti-corruption or anti-bribery training programmes offered or required');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (965,'G1-3_07', 'Percentage of functions-at-risk covered by training programmes');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (966,'G1-3_08', 'Information about members of administrative, supervisory and management bodies relating to anti-corruption or anti-bribery training');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (967,'G1-3_09', 'Disclosure of an analysis of its training activities by, for example, region of training or category');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (968,'G1.MDR-A_01-12', 'Action plans and resources to manage its material impacts, risks, and opportunities related to corruption and bribery [see ESRS 2 - MDR-A]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (969,'G1-4_01', 'Number of convictions for violation of anti-corruption and anti- bribery laws');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (970,'G1-4_02', 'Amount of fines for violation of anti-corruption and anti- bribery laws');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (971,'G1-4_03', 'Prevention and detection of corruption or bribery - anti-corruption and bribery training table');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (972,'G1-4_04', 'Number of confirmed incidents of corruption or bribery');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (973,'G1-4_05', 'Information about nature of confirmed incidents of corruption or bribery');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (974,'G1-4_06', 'Number of confirmed incidents in which own workers were dismissed or disciplined for corruption or bribery-related incidents');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (975,'G1-4_07', 'Number of confirmed incidents relating to contracts with business partners that were terminated or not renewed due to violations related to corruption or bribery');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (976,'G1-4_08', 'Information about details of public legal cases regarding corruption or bribery brought against undertaking and own workers and about outcomes of such cases');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (977,'G1-5_01', 'Information about representative(s) responsible in administrative, management and supervisory bodies for oversight of political influence and lobbying activities');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (978,'G1-5_02', 'Information about financial or in-kind political contributions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (979,'G1-5_03', 'Financial political contributions made');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (980,'G1-5_04', 'Amount of internal and external lobbying expenses');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (981,'G1-5_05', 'Amount paid for membership to lobbying associations');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (982,'G1-5_06', 'In-kind political contributions made');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (983,'G1-5_07', 'Disclosure of how monetary value of in-kind contributions is estimated');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (984,'G1-5_08', 'Financial and in-kind political contributions made [table]');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (985,'G1-5_09', 'Disclosure of main topics covered by lobbying activities and undertaking''s main positions on these topics');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (986,'G1-5_10', 'Undertaking is registered in EU Transparency Register or in equivalent transparency register in Member State');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (987,'G1-5_11', 'Information about appointment of any members of administrative, management and supervisory bodies who held comparable position in public administration in two years preceding such appointment');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (988,'G1-5_12', 'The entity is legally obliged to be a member of a chamber of commerce or other organisation that represents its interests');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (989,'G1.MDR-A_13-14', 'Disclosures to be reported if the undertaking has not adopted actions');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (990,'G1-6_01', 'Average number of days to pay invoice from date when contractual or statutory term of payment starts to be calculated');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (991,'G1-6_02', 'Description of undertakings standard payment terms in number of days by main category of suppliers');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (992,'G1-6_03', 'Percentage of payments aligned with standard payment terms');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (993,'G1-6_04', 'Number of outstanding legal proceedings for late payments');
            INSERT INTO esrs_datapoint (id, "datapointId", name) VALUES (994,'G1-6_05', 'Disclosure of contextual information regarding payment practices');
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
        TRUNCATE TABLE esrs_topic CASCADE;
        TRUNCATE TABLE esrs_disclosure_requirement CASCADE;
        TRUNCATE TABLE esrs_topic_disclosure_requirement CASCADE;
        TRUNCATE TABLE esrs_datapoint CASCADE;
    `);
    }
}
exports.SchemaUpdate1728998052756 = SchemaUpdate1728998052756;
//# sourceMappingURL=1728998052756-schema-update.js.map