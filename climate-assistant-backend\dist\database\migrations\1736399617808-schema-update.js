"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1736399617808 = void 0;
class SchemaUpdate1736399617808 {
    constructor() {
        this.name = 'SchemaUpdate1736399617808';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TYPE "public"."document_status_enum" ADD VALUE 'queued_for_extraction'`);
        await queryRunner.query(`ALTER TYPE "public"."document_status_enum" ADD VALUE 'failed_extraction'`);
        await queryRunner.query(`ALTER TYPE "public"."document_status_enum" ADD VALUE 'queued_for_linking'`);
        await queryRunner.query(`ALTER TYPE "public"."document_status_enum" ADD VALUE 'failed_linking'`);
    }
    async down(queryRunner) { }
}
exports.SchemaUpdate1736399617808 = SchemaUpdate1736399617808;
//# sourceMappingURL=1736399617808-schema-update.js.map