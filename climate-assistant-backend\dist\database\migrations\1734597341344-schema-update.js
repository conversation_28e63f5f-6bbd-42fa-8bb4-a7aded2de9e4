"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1734597341344 = void 0;
class SchemaUpdate1734597341344 {
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_request" ADD "metadata" text`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_request" DROP COLUMN "metadata"`);
    }
}
exports.SchemaUpdate1734597341344 = SchemaUpdate1734597341344;
//# sourceMappingURL=1734597341344-schema-update.js.map