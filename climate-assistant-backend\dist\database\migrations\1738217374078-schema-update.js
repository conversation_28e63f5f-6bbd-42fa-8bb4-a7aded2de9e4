"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1738217374078 = void 0;
class SchemaUpdate1738217374078 {
    constructor() {
        this.name = 'SchemaUpdate1738217374078';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_generation" ADD "createdAt" TIMESTAMP DEFAULT now()`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_generation" DROP COLUMN "createdAt"`);
    }
}
exports.SchemaUpdate1738217374078 = SchemaUpdate1738217374078;
//# sourceMappingURL=1738217374078-schema-update.js.map