"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1750772723874 = void 0;
const constants_1 = require("../../constants");
class SchemaUpdate1750772723874 {
    constructor() {
        this.name = 'SchemaUpdate1750772723874';
        this.newPermissions = [
            constants_1.SystemPermissions.VIEW_ALL_PROMPTS,
            constants_1.SystemPermissions.CREATE_PROMPTS,
            constants_1.SystemPermissions.EDIT_PROMPTS,
            constants_1.SystemPermissions.VIEW_PROMPT_DETAILS,
            constants_1.SystemPermissions.VIEW_PROMPT_METADATA,
            constants_1.SystemPermissions.VIEW_PROMPT_HISTORY,
            constants_1.SystemPermissions.TEST_PROMPTS,
            constants_1.SystemPermissions.VIEW_PROMPT_MODELS,
            constants_1.SystemPermissions.VIEW_PROMPTS_BY_FEATURE,
            constants_1.SystemPermissions.VIEW_ALL_DATAPOINTS,
        ];
    }
    getPermissionDescription(permission) {
        const descriptions = {
            [constants_1.SystemPermissions.VIEW_ALL_PROMPTS]: 'View all prompts in the system',
            [constants_1.SystemPermissions.CREATE_PROMPTS]: 'Create new prompts',
            [constants_1.SystemPermissions.EDIT_PROMPTS]: 'Edit existing prompts',
            [constants_1.SystemPermissions.VIEW_PROMPT_DETAILS]: 'View detailed information about prompts',
            [constants_1.SystemPermissions.VIEW_PROMPT_METADATA]: 'View prompt metadata including usage statistics',
            [constants_1.SystemPermissions.VIEW_PROMPT_HISTORY]: 'View prompt version history and changes',
            [constants_1.SystemPermissions.TEST_PROMPTS]: 'Test prompts with sample data',
            [constants_1.SystemPermissions.VIEW_PROMPT_MODELS]: 'View available LLM models for prompts',
            [constants_1.SystemPermissions.VIEW_PROMPTS_BY_FEATURE]: 'View prompts filtered by feature category',
            [constants_1.SystemPermissions.VIEW_ALL_DATAPOINTS]: 'View all datapoints in the workspace',
        };
        return descriptions[permission] || `Permission ${permission}`;
    }
    async up(queryRunner) {
        const allPermissions = Object.values(constants_1.SystemPermissions);
        const enumValues = allPermissions.map((value) => `'${value}'`).join(', ');
        await queryRunner.query(`ALTER TYPE "public"."permission_name_enum" RENAME TO "permission_name_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."permission_name_enum" AS ENUM (${enumValues})`);
        await queryRunner.query(`ALTER TABLE "permission" ALTER COLUMN "name" TYPE "public"."permission_name_enum" USING "name"::text::"public"."permission_name_enum"`);
        await queryRunner.query(`DROP TYPE "public"."permission_name_enum_old"`);
        for (const permission of this.newPermissions) {
            await queryRunner.query(`
                INSERT INTO "permission" (name, description)
                VALUES ($1, $2)
                ON CONFLICT (name) DO NOTHING
                `, [permission, this.getPermissionDescription(permission)]);
        }
        await queryRunner.query(`
            WITH role_data AS (
                SELECT id FROM "role" WHERE name = 'SUPER_ADMIN'
            ),
            permission_data AS (
                SELECT id FROM "permission" WHERE name = ANY($1)
            )
            INSERT INTO "role_permission" ("roleId", "permissionId")
            SELECT rd.id, pd.id
            FROM role_data rd
            CROSS JOIN permission_data pd
            ON CONFLICT ("roleId", "permissionId") DO NOTHING
            `, [this.newPermissions]);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            DELETE FROM "role_permission" 
            WHERE "permissionId" IN (
                SELECT id FROM "permission" WHERE name = ANY($1)
            )
            `, [this.newPermissions]);
        await queryRunner.query(`DELETE FROM "permission" WHERE name = ANY($1)`, [
            this.newPermissions,
        ]);
    }
}
exports.SchemaUpdate1750772723874 = SchemaUpdate1750772723874;
//# sourceMappingURL=1750772723874-schema-update.js.map