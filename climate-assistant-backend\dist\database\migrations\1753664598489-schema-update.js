"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1753664598489 = void 0;
class SchemaUpdate1753664598489 {
    constructor() {
        this.name = 'SchemaUpdate1753664598489';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TYPE "public"."data_request_generation_status_enum" RENAME TO "data_request_generation_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."data_request_generation_status_enum" AS ENUM('pending', 'approved', 'rejected', 'minorChanges')`);
        await queryRunner.query(`ALTER TABLE "data_request_generation" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "data_request_generation" ALTER COLUMN "status" TYPE "public"."data_request_generation_status_enum" USING "status"::"text"::"public"."data_request_generation_status_enum"`);
        await queryRunner.query(`ALTER TABLE "data_request_generation" ALTER COLUMN "status" SET DEFAULT 'pending'`);
        await queryRunner.query(`DROP TYPE "public"."data_request_generation_status_enum_old"`);
    }
    async down(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."data_request_generation_status_enum_old" AS ENUM('approved', 'pending', 'rejected')`);
        await queryRunner.query(`ALTER TABLE "data_request_generation" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "data_request_generation" ALTER COLUMN "status" TYPE "public"."data_request_generation_status_enum_old" USING "status"::"text"::"public"."data_request_generation_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "data_request_generation" ALTER COLUMN "status" SET DEFAULT 'pending'`);
        await queryRunner.query(`DROP TYPE "public"."data_request_generation_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."data_request_generation_status_enum_old" RENAME TO "data_request_generation_status_enum"`);
    }
}
exports.SchemaUpdate1753664598489 = SchemaUpdate1753664598489;
//# sourceMappingURL=1753664598489-schema-update.js.map