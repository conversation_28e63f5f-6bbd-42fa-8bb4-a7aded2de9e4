"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1754295379433 = void 0;
class SchemaUpdate1754295379433 {
    constructor() {
        this.name = 'SchemaUpdate1754295379433';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_topic" DROP CONSTRAINT "FK_20b7dc49a0bf93c2ccee7b7e4db"`);
        await queryRunner.query(`ALTER TABLE "esrs_topic" ALTER COLUMN "parentId" SET NOT NULL`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b8d38847018497d8a6121b3abb"`);
        await queryRunner.query(`ALTER TYPE "public"."comment_commentabletype_enum_old" RENAME TO "comment_commentabletype_enum_old_old"`);
        await queryRunner.query(`CREATE TYPE "public"."comment_commentabletype_enum" AS ENUM('datapoint_request', 'data_request')`);
        await queryRunner.query(`ALTER TABLE "comment" ALTER COLUMN "commentableType" TYPE "public"."comment_commentabletype_enum" USING "commentableType"::"text"::"public"."comment_commentabletype_enum"`);
        await queryRunner.query(`DROP TYPE "public"."comment_commentabletype_enum_old_old"`);
        await queryRunner.query(`CREATE INDEX "IDX_b8d38847018497d8a6121b3abb" ON "comment" ("commentableId", "commentableType") `);
        await queryRunner.query(`ALTER TABLE "esrs_topic" ADD CONSTRAINT "FK_20b7dc49a0bf93c2ccee7b7e4db" FOREIGN KEY ("parentId") REFERENCES "esrs_topic"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_topic" DROP CONSTRAINT "FK_20b7dc49a0bf93c2ccee7b7e4db"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b8d38847018497d8a6121b3abb"`);
        await queryRunner.query(`CREATE TYPE "public"."comment_commentabletype_enum_old_old" AS ENUM('datapoint_request', 'data_request')`);
        await queryRunner.query(`ALTER TABLE "comment" ALTER COLUMN "commentableType" TYPE "public"."comment_commentabletype_enum_old_old" USING "commentableType"::"text"::"public"."comment_commentabletype_enum_old_old"`);
        await queryRunner.query(`DROP TYPE "public"."comment_commentabletype_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."comment_commentabletype_enum_old_old" RENAME TO "comment_commentabletype_enum_old"`);
        await queryRunner.query(`CREATE INDEX "IDX_b8d38847018497d8a6121b3abb" ON "comment" ("commentableId", "commentableType") `);
        await queryRunner.query(`ALTER TABLE "esrs_topic" ALTER COLUMN "parentId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "esrs_topic" ADD CONSTRAINT "FK_20b7dc49a0bf93c2ccee7b7e4db" FOREIGN KEY ("parentId") REFERENCES "esrs_topic"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
exports.SchemaUpdate1754295379433 = SchemaUpdate1754295379433;
//# sourceMappingURL=1754295379433-schema-update.js.map