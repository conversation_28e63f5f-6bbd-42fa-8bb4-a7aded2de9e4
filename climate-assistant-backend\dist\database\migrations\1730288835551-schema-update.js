"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1730288835551 = void 0;
class SchemaUpdate1730288835551 {
    constructor() {
        this.name = 'SchemaUpdate1730288835551';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "version_history" DROP COLUMN "version_data"`);
        await queryRunner.query(`ALTER TABLE "version_history" DROP COLUMN "type"`);
        await queryRunner.query(`ALTER TABLE "version_history" ADD "event" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "version_history" ADD "ref" uuid NOT NULL`);
        await queryRunner.query(`ALTER TABLE "version_history" ADD "versionData" json NOT NULL`);
        await queryRunner.query(`ALTER TABLE "version_history" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`CREATE INDEX "IDX_1c0f9ac1bd6cabda5b40031637" ON "version_history" ("workspaceId") `);
        await queryRunner.query(`CREATE INDEX "IDX_65c4626d87cf0ab3ae98dec6d0" ON "version_history" ("ref") `);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "public"."IDX_65c4626d87cf0ab3ae98dec6d0"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1c0f9ac1bd6cabda5b40031637"`);
        await queryRunner.query(`ALTER TABLE "version_history" DROP COLUMN "createdAt"`);
        await queryRunner.query(`ALTER TABLE "version_history" DROP COLUMN "versionData"`);
        await queryRunner.query(`ALTER TABLE "version_history" DROP COLUMN "ref"`);
        await queryRunner.query(`ALTER TABLE "version_history" DROP COLUMN "event"`);
        await queryRunner.query(`ALTER TABLE "version_history" ADD "type" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "version_history" ADD "version_data" json NOT NULL`);
    }
}
exports.SchemaUpdate1730288835551 = SchemaUpdate1730288835551;
//# sourceMappingURL=1730288835551-schema-update.js.map