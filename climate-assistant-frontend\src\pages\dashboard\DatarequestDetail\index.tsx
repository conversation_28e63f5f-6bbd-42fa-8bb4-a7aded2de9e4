import React, { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

import PageLoading from './loading';

import { MainLayout } from '@/components/MainLayout';
import { Label } from '@/components/ui/label';
import { MemberSelector } from '@/components/dashboard/MemberSelector';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { InfoboxCompletionSteps } from '@/components/dashboard/InfoboxCompletionSteps';
import { ReportText } from '@/components/dashboard/ReportText';
import { CommentSection } from '@/components/dashboard/Comments';
import { DueDatePicker } from '@/components/dashboard/DueDate';
import {
  CommentType,
  DatapointRequestStatus,
  DataRequestStatus,
} from '@/types';
import { useDataRequest } from '@/hooks/useDataRequest';
import { dataRequestStatuses } from '@/components/DashboardTableConfig';
import { CommentGenerationSection } from '@/components/dashboard/super-admin-tools/CommentGenerations';
import { Button } from '@/components/ui/button';
import { GenerateBulkDatapointsDialog } from '@/components/GenerateBulkDatapointsDialog';
import { ReviewDatapointsDialog } from '@/components/ReviewDatapointsDialog';
import { QUEUE_STATUS } from '@/types/project';
import { fetchAllMembers } from '@/api/workspace-settings/workspace-settings.api';
import { DataRequestProvider } from '@/context/dataRequestContext';
import { Accordion } from '@/components/ui/accordion';
import { DatapointRequestItem } from '@/components/dashboard/DatapointRequestItem';
import { StatusSelector } from '@/components/ui/statusSelector';
import ConfirmDialog from '@/components/ConfirmDialog';
import { IUser } from '@/types/user';
import { usePermissions } from '@/context/permissionsContext';

export const DataRequestDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [datapointGenerationInProgress, setDatapointGenerationInProgress] =
    useState(false);
  const [datapointReviewInProgress, setDatapointReviewInProgress] =
    useState(false);
  const [showReviewDialog, setShowReviewDialog] = useState(false);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);
  const [members, setMembers] = useState<Pick<IUser, 'id' | 'name'>[]>([]);
  const [showStatusConfirm, setShowStatusConfirm] = useState(false);
  const [pendingStatus, setPendingStatus] = useState<DataRequestStatus | null>(
    null
  );

  const {
    dataRequest,
    refetchDataRequest,
    updateResponsiblePerson,
    updateDueDate,
    handleDataRequestStatusChange,
    progress,
    handleBulkGenerateDatapoints,
    handleBulkReviewDatapoints,
  } = useDataRequest(id!);

  const { userPermissions } = usePermissions();

  useEffect(() => {
    fetchAllMembers().then((users) => {
      const members = users.map((user) => ({
        id: user.id,
        name: user.name || user.email,
      }));
      setMembers(members);
    });
  }, []);

  const datapointsToGenerate = useMemo(() => {
    if (!dataRequest) return [];
    return dataRequest.datapointRequests
      .filter((data) => {
        return (
          data.status === DatapointRequestStatus.IncompleteData &&
          data.documentChunkCount &&
          data.documentChunkCount > 0
        );
      })
      .map((data) => data.id);
  }, [dataRequest]);

  const datapointsToReview = useMemo(() => {
    if (!dataRequest) return [];
    return dataRequest.datapointRequests
      .filter((data) => data.status !== DatapointRequestStatus.NotReported)
      .map((data) => data.id);
  }, [dataRequest]);

  useEffect(() => {
    if (!dataRequest) return;
    setDatapointGenerationInProgress(
      dataRequest.datapointRequests.some(
        (req) => req.queueStatus === QUEUE_STATUS.QueuedForGeneration
      )
    );
    setDatapointReviewInProgress(
      dataRequest.datapointRequests.some(
        (req) => req.queueStatus === QUEUE_STATUS.QueuedForReview
      )
    );
  }, [dataRequest]);

  useEffect(() => {
    setDatapointGenerationInProgress(
      dataRequest?.queueStatus === QUEUE_STATUS.QueuedForGeneration
    );
    setDatapointReviewInProgress(
      dataRequest?.queueStatus === QUEUE_STATUS.QueuedForReview
    );
  }, [dataRequest?.queueStatus]);

  const handleStatusChange = async (newStatus: DataRequestStatus) => {
    if (newStatus === DataRequestStatus.Complete) {
      const hasIncompleteDatapoints = dataRequest!.datapointRequests.some(
        (datapoint) =>
          datapoint.status === DatapointRequestStatus.IncompleteData
      );

      if (hasIncompleteDatapoints) {
        setPendingStatus(newStatus);
        setShowStatusConfirm(true);
        return;
      }
    }

    await handleDataRequestStatusChange(newStatus);
    await refetchDataRequest();
  };

  const handleStatusConfirm = async () => {
    if (pendingStatus) {
      await handleDataRequestStatusChange(pendingStatus);
      setPendingStatus(null);
    }
    setShowStatusConfirm(false);
    await refetchDataRequest();
  };

  if (!dataRequest) return <PageLoading />;

  return (
    <MainLayout>
      <DataRequestProvider refetchDataRequest={refetchDataRequest}>
        <div className="space-y-5 mb-16">
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl tracking-wide">
                {dataRequest.disclosureRequirement.dr}:{' '}
                <span className="font-bold">
                  {dataRequest.disclosureRequirement.name}
                </span>
              </h2>
              <div className="flex items-center space-x-2">
                {userPermissions.canPerformBulkOperationsOnDr &&
                  datapointsToGenerate.length > 0 && (
                    <>
                      <Button
                        className="rounded-full px-4 flex items-center"
                        onClick={() => setShowGenerateDialog(true)}
                        disabled={datapointGenerationInProgress}
                      >
                        {datapointGenerationInProgress
                          ? 'Generating'
                          : 'Generate'}{' '}
                        for {datapointsToGenerate.length} Datapoints
                      </Button>
                      <GenerateBulkDatapointsDialog
                        dataRequestId={dataRequest.id}
                        open={showGenerateDialog}
                        onOpenChange={setShowGenerateDialog}
                        onGenerate={handleBulkGenerateDatapoints}
                        datapointCount={datapointsToGenerate.length}
                        isGenerating={datapointGenerationInProgress}
                        setIsGenerating={setDatapointGenerationInProgress}
                      />
                    </>
                  )}
                {userPermissions.canPerformBulkOperationsOnDr &&
                  datapointsToReview.length > 0 && (
                    <>
                      <Button
                        className="rounded-full px-4 flex items-center"
                        onClick={() => setShowReviewDialog(true)}
                        disabled={datapointReviewInProgress}
                      >
                        {datapointReviewInProgress ? 'Reviewing' : 'Review'} for{' '}
                        {datapointsToReview.length} Datapoints
                      </Button>
                      <ReviewDatapointsDialog
                        dataRequestId={dataRequest.id}
                        open={showReviewDialog}
                        onOpenChange={setShowReviewDialog}
                        onReview={handleBulkReviewDatapoints}
                        datapointCount={datapointsToReview.length}
                        isReviewing={datapointReviewInProgress}
                        setIsReviewing={setDatapointReviewInProgress}
                      />
                    </>
                  )}
              </div>
            </div>
            <div className="flex justify-start items-center gap-10">
              <div className="flex items-center space-x-3">
                <Label>Status: </Label>
                <StatusSelector
                  currentStatus={dataRequest.status}
                  statuses={dataRequestStatuses}
                  onStatusChange={handleStatusChange}
                  disabled={!userPermissions.canEditDr}
                />
              </div>
              <div className="flex items-center space-x-3">
                <Label>Responsible: </Label>
                <MemberSelector
                  members={members}
                  action={updateResponsiblePerson}
                  currentUser={dataRequest.responsiblePerson?.id}
                  placeholder="Select User"
                  disabled={!userPermissions.canAssignDr}
                />
              </div>
              <div className="flex items-center space-x-3">
                <Label>Complete Until: </Label>
                <DueDatePicker
                  currentDueDate={dataRequest.dueDate}
                  action={updateDueDate}
                />
              </div>
            </div>
          </div>

          <Tabs defaultValue="information" className="w-full">
            <TabsList className="grid grid-cols-2 gap-1 mb-5 w-fit">
              <TabsTrigger value="information" className="h-8 mb-1">
                Information for Datapoints ({progress()})
              </TabsTrigger>
              <TabsTrigger value="reporttext" className="h-8 mb-1">
                Reporttext for {dataRequest.disclosureRequirement.dr}{' '}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="information" className="space-y-5">
              <h3 className="text-xl mb-2 tracking-wide">
                <span className="font-bold">Information for Datapoints </span>(
                {progress()})
              </h3>

              <InfoboxCompletionSteps variant={'blue'} />

              <Accordion type="multiple" className="w-full space-y-3">
                {dataRequest.datapointRequests.map(
                  (datapointRequest, index) => (
                    <DatapointRequestItem
                      key={index}
                      datapointRequest={datapointRequest}
                      projectId={dataRequest.projectId}
                      members={members}
                    />
                  )
                )}
              </Accordion>
            </TabsContent>

            <TabsContent value="reporttext">
              <h3 className="text-xl mb-2 tracking-wide">
                <span className="font-bold">
                  Reporttext for {dataRequest.disclosureRequirement.dr}{' '}
                </span>
              </h3>
              <ReportText
                dataRequest={dataRequest}
                members={members}
                updateCallback={refetchDataRequest}
              />
              {userPermissions.canPerformAiGenerateOrReviewOnDr && (
                <CommentGenerationSection
                  projectId={dataRequest.projectId}
                  savedComments={dataRequest.commentGenerations}
                  updateCallback={refetchDataRequest}
                />
              )}
              {userPermissions.canAddComments && (
                <CommentSection
                  savedComments={dataRequest.comments}
                  projectId={dataRequest.projectId}
                  commentableId={dataRequest.id}
                  commentableType={CommentType.DataRequest}
                  updateCallback={refetchDataRequest}
                />
              )}
            </TabsContent>
          </Tabs>

          {/* Save Button */}
          <ConfirmDialog
            title="Confirm Status Change"
            description="There are still some incomplete datapoints in this Disclosure Requirement. Make sure to complete those first, before marking the Disclosure Requirement as complete."
            open={showStatusConfirm}
            onConfirm={handleStatusConfirm}
            onClose={() => setShowStatusConfirm(false)}
          />
        </div>
      </DataRequestProvider>
    </MainLayout>
  );
};
