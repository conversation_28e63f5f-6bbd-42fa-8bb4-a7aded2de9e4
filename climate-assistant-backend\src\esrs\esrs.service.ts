import { Injectable } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository, In, Not } from 'typeorm';
import { LlmService } from '../llm/services/llm.service';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { ESRSDisclosureRequirement } from './entities/esrs-disclosure-requirement.entity';
import { ESRSTopic } from './entities/esrs-topic.entity';

@Injectable()
export class EsrsService {
  constructor(
    @InjectRepository(ESRSDatapoint)
    private readonly esrsDatapointRepository: Repository<ESRSDatapoint>,
    @InjectRepository(ESRSDisclosureRequirement)
    private readonly esrsDisclosureRequirementRepository: Repository<ESRSDisclosureRequirement>,
    @InjectRepository(ESRSTopic)
    private readonly esrsTopicRepository: Repository<ESRSTopic>,
    @InjectDataSource() private dataSource: DataSource,
    private readonly chatGptService: LlmService
  ) {}

  async getEsrsDatapointsByStandard(esrs: string): Promise<ESRSDatapoint[]> {
    const drs =
      esrs === 'all'
        ? await this.esrsDisclosureRequirementRepository.find()
        : await this.esrsDisclosureRequirementRepository.find({
            where: { esrs: esrs },
          });

    const datapoints = await this.esrsDatapointRepository.find({
      where: {
        esrsDisclosureRequirementId: In(drs.map((dr) => dr.id)),
      },
      order: {
        esrsDisclosureRequirement: {
          esrs: 'ASC',
          sort: 'ASC',
        },
        datapointId: 'ASC',
      },
    });

    return datapoints;
  }

  async getEsrsTopics(): Promise<ESRSTopic[]> {
    return this.esrsTopicRepository.find({
      where: { level: 'topic', id: Not(11) },
      relations: ['children.children'],
    });
  }
}
