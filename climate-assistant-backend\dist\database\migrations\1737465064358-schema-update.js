"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1737465064358 = void 0;
class SchemaUpdate1737465064358 {
    constructor() {
        this.name = 'SchemaUpdate1737465064358';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."comment_generation_status_enum" AS ENUM('under_approval', 'approved', 'rejected', 'unchecked')`);
        await queryRunner.query(`CREATE TABLE "comment_generation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "userId" uuid NOT NULL, "commentableId" uuid NOT NULL, "commentableType" "public"."comment_commentabletype_enum" NOT NULL, "comment" text NOT NULL, "resolved" boolean NOT NULL, "status" "public"."comment_generation_status_enum" NOT NULL DEFAULT 'unchecked', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "evaluatorId" uuid, "evaluatorComment" character varying, "evaluatedAt" TIMESTAMP, CONSTRAINT "PK_3aae3f939beb9cafe73f473d45d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_9b2369479bbdd96d6a446437a4" ON "comment_generation" ("commentableId", "commentableType") `);
        await queryRunner.query(`ALTER TABLE "comment_generation" ADD CONSTRAINT "FK_fa3f507677dc586d6d9aa1f6c23" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "comment_generation" DROP CONSTRAINT "FK_fa3f507677dc586d6d9aa1f6c23"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9b2369479bbdd96d6a446437a4"`);
        await queryRunner.query(`DROP TABLE "comment_generation"`);
        await queryRunner.query(`DROP TYPE "public"."comment_generation_status_enum"`);
    }
}
exports.SchemaUpdate1737465064358 = SchemaUpdate1737465064358;
//# sourceMappingURL=1737465064358-schema-update.js.map