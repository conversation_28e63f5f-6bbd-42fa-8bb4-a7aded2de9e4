"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1741582845215 = void 0;
class SchemaUpdate1741582845215 {
    constructor() {
        this.name = 'SchemaUpdate1741582845215';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "workspace" DROP COLUMN "reportTextGenerationRules"`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "workspace" ADD "reportTextGenerationRules" text`);
    }
}
exports.SchemaUpdate1741582845215 = SchemaUpdate1741582845215;
//# sourceMappingURL=1741582845215-schema-update.js.map