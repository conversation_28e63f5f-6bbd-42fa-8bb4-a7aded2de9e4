import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ESRSTopicDisclosureRequirement } from './esrs_topic_disclosure_requirement.entity';
import { ESRSDatapoint } from '../../datapoint/entities/esrs-datapoint.entity';
import { DataRequest } from '../../data-request/entities/data-request.entity';

@Entity()
export class ESRSDisclosureRequirement {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int' })
  sort: number;

  @Column({ type: 'varchar' })
  dr: string;

  @Column({ type: 'varchar' })
  esrs: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'text', nullable: true })
  drDescription?: string;

  @Column({ type: 'text', nullable: true })
  drObjective?: string;

  @Column({ type: 'text', nullable: true })
  lawText?: string;

  @Column({ type: 'text', nullable: true })
  lawTextAR?: string;

  @Column({ type: 'boolean', default: false })
  publicAccess: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(
    () => ESRSTopicDisclosureRequirement,
    (relation) => relation.disclosureRequirement,
  )
  topicRelations: ESRSTopicDisclosureRequirement[];

  @OneToMany(
    () => ESRSDatapoint,
    (esrsDatapoint) => esrsDatapoint.esrsDisclosureRequirement,
  )
  esrsDatapoints: ESRSDatapoint[];

  @OneToMany(
    () => DataRequest,
    (dataRequest) => dataRequest.disclosureRequirement,
  )
  dataRequests: DataRequest[];
}
