"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1748257193777 = void 0;
class SchemaUpdate1748257193777 {
    constructor() {
        this.name = 'SchemaUpdate1748257193777';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."datapoint_request_status_enum_new" AS ENUM('incomplete_data', 'complete_data', 'not_reported')`);
        await queryRunner.query(`
      ALTER TABLE "datapoint_request" 
      ALTER COLUMN status TYPE "public"."datapoint_request_status_enum_new" 
      USING (
        CASE status::text
          WHEN 'not_answered' THEN 'not_reported'
          WHEN 'no_data' THEN 'incomplete_data'
          WHEN 'draft' THEN 'incomplete_data'
          WHEN 'approved_answer' THEN 'complete_data'
          WHEN 'incomplete_data' THEN 'incomplete_data'
          WHEN 'complete_data' THEN 'complete_data'
          ELSE 'incomplete_data'
        END
      )::"public"."datapoint_request_status_enum_new"
    `);
        await queryRunner.query(`DROP TYPE "public"."datapoint_request_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."datapoint_request_status_enum_new" RENAME TO "datapoint_request_status_enum"`);
    }
    async down(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."datapoint_request_status_enum_old" AS ENUM('not_answered', 'incomplete_data', 'no_data', 'complete_data')`);
        await queryRunner.query(`ALTER TABLE "datapoint_request" ALTER COLUMN "status" TYPE "public"."datapoint_request_status_enum_old" USING "status"::"text"::"public"."datapoint_request_status_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."datapoint_request_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."datapoint_request_status_enum_old" RENAME TO "datapoint_request_status_enum"`);
    }
}
exports.SchemaUpdate1748257193777 = SchemaUpdate1748257193777;
//# sourceMappingURL=1748257193777-schema-update.js.map