"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1721910372542 = void 0;
class SchemaUpdate1721910372542 {
    constructor() {
        this.name = 'SchemaUpdate1721910372542';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "chat_message"
          ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "chat_history"
          ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "chat_history" DROP COLUMN "createdAt"`);
        await queryRunner.query(`ALTER TABLE "chat_message" DROP COLUMN "createdAt"`);
    }
}
exports.SchemaUpdate1721910372542 = SchemaUpdate1721910372542;
//# sourceMappingURL=1721910372542-schema-update.js.map