"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1728642599474 = void 0;
class SchemaUpdate1728642599474 {
    constructor() {
        this.name = 'SchemaUpdate1728642599474';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."token_type_enum" AS ENUM('passwordReset', 'workspaceInvite')`);
        await queryRunner.query(`CREATE TABLE "token" ("userId" uuid NOT NULL, "token" character varying NOT NULL, "type" "public"."token_type_enum" NOT NULL, "expiresAt" TIMESTAMP NOT NULL, "createdAt" TIMESTAMP NOT NULL, CONSTRAINT "PK_94f168faad896c0786646fa3d4a" PRIMARY KEY ("userId"))`);
        await queryRunner.query(`CREATE TABLE "esrs_topic" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, CONSTRAINT "PK_4b25d10239ca5dcce37209c77fa" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "esrs_disclosure_requirement" ("id" SERIAL NOT NULL, "sort" integer NOT NULL, "dr" character varying NOT NULL, "esrs" character varying NOT NULL, "name" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL, "updatedAt" TIMESTAMP NOT NULL, CONSTRAINT "PK_9459053b627af75036f7049840b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "material_esrs_topic" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "projectId" uuid, "esrsTopicId" integer, CONSTRAINT "PK_397ad6d4cdc031f4628570db5b7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."project_primarycontentlanguage_enum" AS ENUM('DE', 'EN')`);
        await queryRunner.query(`CREATE TABLE "project" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "workspaceId" uuid NOT NULL, "name" character varying NOT NULL, "primaryContentLanguage" "public"."project_primarycontentlanguage_enum" NOT NULL, "createdBy" uuid NOT NULL, "createdAt" TIMESTAMP NOT NULL, CONSTRAINT "PK_4d68b1358bb5b766d3e78f32f57" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "document" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "workspaceId" uuid NOT NULL, "name" character varying NOT NULL, "path" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_e57d3357f83f3cdc0acffc3d777" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "document_chunk" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "documentId" uuid NOT NULL, "page" integer NOT NULL, "content" text NOT NULL, CONSTRAINT "PK_70d9772bf367d82f9b7e568c87c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "datapoint_document_chunk" ("id" SERIAL NOT NULL, "datapointRequestId" uuid, "documentChunkId" uuid, CONSTRAINT "PK_fd50f93691bc68653d701ab368f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "esrs_datapoint" ("id" SERIAL NOT NULL, "datapointId" character varying NOT NULL, "name" character varying NOT NULL, CONSTRAINT "PK_d1a8f0de141fc60a362019e1426" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."datapoint_request_status_enum" AS ENUM('not_answered', 'incomplete_data', 'no_data', 'complete_data')`);
        await queryRunner.query(`CREATE TABLE "datapoint_request" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "dataRequestId" uuid NOT NULL, "esrsDatapointId" integer NOT NULL, "content" text NOT NULL, "status" "public"."datapoint_request_status_enum" NOT NULL, "createdAt" TIMESTAMP NOT NULL, "updatedAt" TIMESTAMP NOT NULL, CONSTRAINT "PK_415696d9334f1e25643bf5d7390" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."data_request_status_enum" AS ENUM('no_data', 'incomplete_data', 'draft', 'complete_data', 'approved_answer', 'not_answered')`);
        await queryRunner.query(`CREATE TABLE "data_request" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "dataRequestTypeId" integer NOT NULL, "dataRequestType" character varying NOT NULL, "status" "public"."data_request_status_enum" NOT NULL, "content" text NOT NULL, "dueDate" date, "responsiblePersonId" uuid, "approvedBy" uuid, "approvedAt" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL, "updatedAt" TIMESTAMP NOT NULL, "projectId" uuid NOT NULL, CONSTRAINT "PK_cc9c5faceb7bce9bd72ff794bbf" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."comment_commentable_type_enum" AS ENUM('datapoint_request', 'data_request')`);
        await queryRunner.query(`CREATE TABLE "comment" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "userId" uuid NOT NULL, "commentable_id" uuid NOT NULL, "commentable_type" "public"."comment_commentable_type_enum" NOT NULL, "comment" text NOT NULL, "resolved" boolean NOT NULL, "createdAt" TIMESTAMP NOT NULL, CONSTRAINT "PK_0b0e4bbc8415ec426f87f3a88e2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_workspace" ("userId" uuid NOT NULL, "workspaceId" uuid NOT NULL, "role" character varying, "joinedAt" TIMESTAMP NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_c395920dbb9ba8840eaa0278bf8" PRIMARY KEY ("userId", "workspaceId"))`);
        await queryRunner.query(`CREATE TABLE "company" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "generalCompanyProfile" text NOT NULL, "reportTextGenerationRules" text NOT NULL, "workspaceId" uuid NOT NULL, "createdAt" TIMESTAMP NOT NULL, CONSTRAINT "PK_056f7854a7afdba7cbd6d45fc20" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "version_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" character varying NOT NULL, "workspaceId" uuid NOT NULL, "version_data" json NOT NULL, CONSTRAINT "PK_5db259cbb09ce82c0d13cfd1b23" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "workspace" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL, CONSTRAINT "PK_ca86b6f9b3be5fe26d307d09b49" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "esrs_topic_disclosure_requirement" ("esrsTopicId" integer NOT NULL, "esrsDisclosureRequirementId" integer NOT NULL, CONSTRAINT "PK_cb17a28a4b92dce387c2f93952c" PRIMARY KEY ("esrsTopicId", "esrsDisclosureRequirementId"))`);
        await queryRunner.query(`CREATE INDEX "IDX_e208d4f71abc19d8c0a5eaaf26" ON "esrs_topic_disclosure_requirement" ("esrsTopicId") `);
        await queryRunner.query(`CREATE INDEX "IDX_40a3bc24828423a91684a5ce47" ON "esrs_topic_disclosure_requirement" ("esrsDisclosureRequirementId") `);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "companyName"`);
        await queryRunner.query(`ALTER TABLE "user" ADD "name" character varying(100)`);
        await queryRunner.query(`ALTER TABLE "user" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "token" ADD CONSTRAINT "FK_94f168faad896c0786646fa3d4a" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_esrs_topic" ADD CONSTRAINT "FK_50a1eb248445b84935b069fc0c8" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "material_esrs_topic" ADD CONSTRAINT "FK_ed602f423d73ef38205a16a56f7" FOREIGN KEY ("esrsTopicId") REFERENCES "esrs_topic"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "project" ADD CONSTRAINT "FK_c224ab17df530651e53a398ed92" FOREIGN KEY ("workspaceId") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "project" ADD CONSTRAINT "FK_c714b0a5eaf71cc3a36c242d2e9" FOREIGN KEY ("createdBy") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "document" ADD CONSTRAINT "FK_41070461ad429c324c367525371" FOREIGN KEY ("workspaceId") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "document_chunk" ADD CONSTRAINT "FK_3e9a852328831b703e5ef175ca8" FOREIGN KEY ("documentId") REFERENCES "document"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD CONSTRAINT "FK_00a077be01d7bfaf0bcd5f7bbfb" FOREIGN KEY ("datapointRequestId") REFERENCES "datapoint_request"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d" FOREIGN KEY ("documentChunkId") REFERENCES "document_chunk"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "datapoint_request" ADD CONSTRAINT "FK_802ef2f8443c803fce0ea914096" FOREIGN KEY ("dataRequestId") REFERENCES "data_request"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "datapoint_request" ADD CONSTRAINT "FK_0cbeff414e1139c22da793547e5" FOREIGN KEY ("esrsDatapointId") REFERENCES "esrs_datapoint"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "data_request" ADD CONSTRAINT "FK_4572010e83d95ebe4bfbab75431" FOREIGN KEY ("dataRequestTypeId") REFERENCES "esrs_disclosure_requirement"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "data_request" ADD CONSTRAINT "FK_45bd67eaca95cdb8d83c5343633" FOREIGN KEY ("responsiblePersonId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "data_request" ADD CONSTRAINT "FK_7df0e25371a1a5bd8029a5d0569" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comment" ADD CONSTRAINT "FK_c0354a9a009d3bb45a08655ce3b" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_workspace" ADD CONSTRAINT "FK_4ea12fabb12c08c3dc8839d0932" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_workspace" ADD CONSTRAINT "FK_46438fa9a476521c49324b59843" FOREIGN KEY ("workspaceId") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "company" ADD CONSTRAINT "FK_517322010b04597bd8475f6f81e" FOREIGN KEY ("workspaceId") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "version_history" ADD CONSTRAINT "FK_1c0f9ac1bd6cabda5b40031637c" FOREIGN KEY ("workspaceId") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "esrs_topic_disclosure_requirement" ADD CONSTRAINT "FK_e208d4f71abc19d8c0a5eaaf267" FOREIGN KEY ("esrsTopicId") REFERENCES "esrs_topic"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "esrs_topic_disclosure_requirement" ADD CONSTRAINT "FK_40a3bc24828423a91684a5ce477" FOREIGN KEY ("esrsDisclosureRequirementId") REFERENCES "esrs_disclosure_requirement"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_topic_disclosure_requirement" DROP CONSTRAINT "FK_40a3bc24828423a91684a5ce477"`);
        await queryRunner.query(`ALTER TABLE "esrs_topic_disclosure_requirement" DROP CONSTRAINT "FK_e208d4f71abc19d8c0a5eaaf267"`);
        await queryRunner.query(`ALTER TABLE "version_history" DROP CONSTRAINT "FK_1c0f9ac1bd6cabda5b40031637c"`);
        await queryRunner.query(`ALTER TABLE "company" DROP CONSTRAINT "FK_517322010b04597bd8475f6f81e"`);
        await queryRunner.query(`ALTER TABLE "user_workspace" DROP CONSTRAINT "FK_46438fa9a476521c49324b59843"`);
        await queryRunner.query(`ALTER TABLE "user_workspace" DROP CONSTRAINT "FK_4ea12fabb12c08c3dc8839d0932"`);
        await queryRunner.query(`ALTER TABLE "comment" DROP CONSTRAINT "FK_c0354a9a009d3bb45a08655ce3b"`);
        await queryRunner.query(`ALTER TABLE "data_request" DROP CONSTRAINT "FK_7df0e25371a1a5bd8029a5d0569"`);
        await queryRunner.query(`ALTER TABLE "data_request" DROP CONSTRAINT "FK_45bd67eaca95cdb8d83c5343633"`);
        await queryRunner.query(`ALTER TABLE "data_request" DROP CONSTRAINT "FK_4572010e83d95ebe4bfbab75431"`);
        await queryRunner.query(`ALTER TABLE "datapoint_request" DROP CONSTRAINT "FK_0cbeff414e1139c22da793547e5"`);
        await queryRunner.query(`ALTER TABLE "datapoint_request" DROP CONSTRAINT "FK_802ef2f8443c803fce0ea914096"`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d"`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP CONSTRAINT "FK_00a077be01d7bfaf0bcd5f7bbfb"`);
        await queryRunner.query(`ALTER TABLE "document_chunk" DROP CONSTRAINT "FK_3e9a852328831b703e5ef175ca8"`);
        await queryRunner.query(`ALTER TABLE "document" DROP CONSTRAINT "FK_41070461ad429c324c367525371"`);
        await queryRunner.query(`ALTER TABLE "project" DROP CONSTRAINT "FK_c714b0a5eaf71cc3a36c242d2e9"`);
        await queryRunner.query(`ALTER TABLE "project" DROP CONSTRAINT "FK_c224ab17df530651e53a398ed92"`);
        await queryRunner.query(`ALTER TABLE "material_esrs_topic" DROP CONSTRAINT "FK_ed602f423d73ef38205a16a56f7"`);
        await queryRunner.query(`ALTER TABLE "material_esrs_topic" DROP CONSTRAINT "FK_50a1eb248445b84935b069fc0c8"`);
        await queryRunner.query(`ALTER TABLE "token" DROP CONSTRAINT "FK_94f168faad896c0786646fa3d4a"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "createdAt"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "name"`);
        await queryRunner.query(`ALTER TABLE "user" ADD "companyName" character varying NOT NULL DEFAULT ''`);
        await queryRunner.query(`DROP INDEX "public"."IDX_40a3bc24828423a91684a5ce47"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e208d4f71abc19d8c0a5eaaf26"`);
        await queryRunner.query(`DROP TABLE "esrs_topic_disclosure_requirement"`);
        await queryRunner.query(`DROP TABLE "workspace"`);
        await queryRunner.query(`DROP TABLE "version_history"`);
        await queryRunner.query(`DROP TABLE "company"`);
        await queryRunner.query(`DROP TABLE "user_workspace"`);
        await queryRunner.query(`DROP TABLE "comment"`);
        await queryRunner.query(`DROP TYPE "public"."comment_commentable_type_enum"`);
        await queryRunner.query(`DROP TABLE "data_request"`);
        await queryRunner.query(`DROP TYPE "public"."data_request_status_enum"`);
        await queryRunner.query(`DROP TABLE "datapoint_request"`);
        await queryRunner.query(`DROP TYPE "public"."datapoint_request_status_enum"`);
        await queryRunner.query(`DROP TABLE "esrs_datapoint"`);
        await queryRunner.query(`DROP TABLE "datapoint_document_chunk"`);
        await queryRunner.query(`DROP TABLE "document_chunk"`);
        await queryRunner.query(`DROP TABLE "document"`);
        await queryRunner.query(`DROP TABLE "project"`);
        await queryRunner.query(`DROP TYPE "public"."project_primarycontentlanguage_enum"`);
        await queryRunner.query(`DROP TABLE "material_esrs_topic"`);
        await queryRunner.query(`DROP TABLE "esrs_disclosure_requirement"`);
        await queryRunner.query(`DROP TABLE "esrs_topic"`);
        await queryRunner.query(`DROP TABLE "token"`);
        await queryRunner.query(`DROP TYPE "public"."token_type_enum"`);
    }
}
exports.SchemaUpdate1728642599474 = SchemaUpdate1728642599474;
//# sourceMappingURL=1728642599474-schema-update.js.map