"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1737723978093 = void 0;
class SchemaUpdate1737723978093 {
    constructor() {
        this.name = 'SchemaUpdate1737723978093';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_request" ADD "publicAccess" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "data_request" ADD "publicAccess" boolean NOT NULL DEFAULT false`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "data_request" DROP COLUMN "publicAccess"`);
        await queryRunner.query(`ALTER TABLE "datapoint_request" DROP COLUMN "publicAccess"`);
    }
}
exports.SchemaUpdate1737723978093 = SchemaUpdate1737723978093;
//# sourceMappingURL=1737723978093-schema-update.js.map