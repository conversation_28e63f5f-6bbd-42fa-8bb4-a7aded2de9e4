"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddEvaluatorCommentToGenerations1752692782248 = void 0;
class AddEvaluatorCommentToGenerations1752692782248 {
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "datapoint_generation" 
      ADD COLUMN "evaluatorComment" VARCHAR NULL
    `);
        await queryRunner.query(`
      ALTER TABLE "data_request_generation" 
      ADD COLUMN "evaluatorComment" VARCHAR NULL
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "datapoint_generation" 
      DROP COLUMN "evaluatorComment"
    `);
        await queryRunner.query(`
      ALTER TABLE "data_request_generation" 
      DROP COLUMN "evaluatorComment"
    `);
    }
}
exports.AddEvaluatorCommentToGenerations1752692782248 = AddEvaluatorCommentToGenerations1752692782248;
//# sourceMappingURL=1752692782248-schema-update.js.map