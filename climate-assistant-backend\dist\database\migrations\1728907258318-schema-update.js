"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1728907258318 = void 0;
class SchemaUpdate1728907258318 {
    constructor() {
        this.name = 'SchemaUpdate1728907258318';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "company" ALTER COLUMN "generalCompanyProfile" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "company" ALTER COLUMN "reportTextGenerationRules" DROP NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "company" ALTER COLUMN "reportTextGenerationRules" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "company" ALTER COLUMN "generalCompanyProfile" SET NOT NULL`);
    }
}
exports.SchemaUpdate1728907258318 = SchemaUpdate1728907258318;
//# sourceMappingURL=1728907258318-schema-update.js.map