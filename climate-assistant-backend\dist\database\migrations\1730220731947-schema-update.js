"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1730220731947 = void 0;
class SchemaUpdate1730220731947 {
    constructor() {
        this.name = 'SchemaUpdate1730220731947';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "user_workspace" DROP COLUMN "role"`);
        await queryRunner.query(`CREATE TYPE "public"."user_workspace_role_enum" AS ENUM('SUPER_ADMIN', 'WORKSPACE_ADMIN', 'AI_CONTRIBUTOR', 'CONTRIBUTOR')`);
        await queryRunner.query(`ALTER TABLE "user_workspace" ADD "role" "public"."user_workspace_role_enum"`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "user_workspace" DROP COLUMN "role"`);
        await queryRunner.query(`DROP TYPE "public"."user_workspace_role_enum"`);
        await queryRunner.query(`ALTER TABLE "user_workspace" ADD "role" character varying`);
    }
}
exports.SchemaUpdate1730220731947 = SchemaUpdate1730220731947;
//# sourceMappingURL=1730220731947-schema-update.js.map