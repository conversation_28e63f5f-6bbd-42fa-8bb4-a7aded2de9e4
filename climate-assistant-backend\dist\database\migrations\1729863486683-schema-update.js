"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1729863486683 = void 0;
class SchemaUpdate1729863486683 {
    constructor() {
        this.name = 'SchemaUpdate1729863486683';
    }
    async up(queryRunner) {
        await queryRunner.query(`delete from data_request using esrs_disclosure_requirement where data_request."dataRequestTypeId"=esrs_disclosure_requirement."id" and esrs_disclosure_requirement."id" > 95`);
        await queryRunner.query(`delete from esrs_topic_disclosure_requirement using esrs_disclosure_requirement where esrs_topic_disclosure_requirement."esrsDisclosureRequirementId"=esrs_disclosure_requirement."id" and esrs_disclosure_requirement."id" > 95`);
        await queryRunner.query(`delete from esrs_disclosure_requirement where "id" > 95;`);
        await queryRunner.query(`delete from esrs_topic where "id" = 12;`);
        await queryRunner.query(`update esrs_disclosure_requirement set sort = sort + 12;`);
        await queryRunner.query(`update esrs_disclosure_requirement set sort = sort-95 where esrs ='ESRS 2';`);
    }
    async down(queryRunner) { }
}
exports.SchemaUpdate1729863486683 = SchemaUpdate1729863486683;
//# sourceMappingURL=1729863486683-schema-update.js.map