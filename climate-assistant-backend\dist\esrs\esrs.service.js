"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EsrsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const llm_service_1 = require("../llm/services/llm.service");
const esrs_datapoint_entity_1 = require("../datapoint/entities/esrs-datapoint.entity");
const esrs_disclosure_requirement_entity_1 = require("./entities/esrs-disclosure-requirement.entity");
const esrs_topic_entity_1 = require("./entities/esrs-topic.entity");
let EsrsService = class EsrsService {
    constructor(esrsDatapointRepository, esrsDisclosureRequirementRepository, esrsTopicRepository, dataSource, chatGptService) {
        this.esrsDatapointRepository = esrsDatapointRepository;
        this.esrsDisclosureRequirementRepository = esrsDisclosureRequirementRepository;
        this.esrsTopicRepository = esrsTopicRepository;
        this.dataSource = dataSource;
        this.chatGptService = chatGptService;
    }
    async getEsrsDatapointsByStandard(esrs) {
        const drs = esrs === 'all'
            ? await this.esrsDisclosureRequirementRepository.find()
            : await this.esrsDisclosureRequirementRepository.find({
                where: { esrs: esrs },
            });
        const datapoints = await this.esrsDatapointRepository.find({
            where: {
                esrsDisclosureRequirementId: (0, typeorm_2.In)(drs.map((dr) => dr.id)),
            },
            order: {
                esrsDisclosureRequirement: {
                    esrs: 'ASC',
                    sort: 'ASC',
                },
                datapointId: 'ASC',
            },
        });
        return datapoints;
    }
    async getEsrsTopics() {
        return this.esrsTopicRepository.find({
            where: { level: 'topic', id: (0, typeorm_2.Not)(11) },
            relations: ['children.children'],
        });
    }
};
exports.EsrsService = EsrsService;
exports.EsrsService = EsrsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(esrs_datapoint_entity_1.ESRSDatapoint)),
    __param(1, (0, typeorm_1.InjectRepository)(esrs_disclosure_requirement_entity_1.ESRSDisclosureRequirement)),
    __param(2, (0, typeorm_1.InjectRepository)(esrs_topic_entity_1.ESRSTopic)),
    __param(3, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource,
        llm_service_1.LlmService])
], EsrsService);
//# sourceMappingURL=esrs.service.js.map