"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1750046830302 = void 0;
const constants_1 = require("../../constants");
const RolePermissions = {
    SUPER_ADMIN: Object.values(constants_1.SystemPermissions),
    WORKSPACE_ADMIN: [
        constants_1.SystemPermissions.CHANGE_MATERIALITY_SETTING,
        constants_1.SystemPermissions.ACCESS_MATERIALITY_SETTING,
        constants_1.SystemPermissions.CREATE_DATAPOINTS,
        constants_1.SystemPermissions.EDIT_DATAPOINTS,
        constants_1.SystemPermissions.APPROVE_DATAPOINTS,
        constants_1.SystemPermissions.MARK_DATAPOINTS,
        constants_1.SystemPermissions.EXPORT_DATAPOINTS_EXCEL,
        constants_1.SystemPermissions.GAP_ANALYSIS_DATAPOINTS,
        constants_1.SystemPermissions.CREATE_DRS,
        constants_1.SystemPermissions.EDIT_DRS,
        constants_1.SystemPermissions.GAP_ANALYSIS_DRS,
        constants_1.SystemPermissions.APPROVE_DRS,
        constants_1.SystemPermissions.MARK_DRS,
        constants_1.SystemPermissions.ACCESS_VERSION_HISTORY,
        constants_1.SystemPermissions.RESTORE_VERSION_HISTORY,
        constants_1.SystemPermissions.CREATE_COMMENTS,
        constants_1.SystemPermissions.EDIT_COMMENTS,
        constants_1.SystemPermissions.EDIT_WORKSPACE_SETTINGS,
        constants_1.SystemPermissions.EDIT_PROJECT_SETTINGS,
        constants_1.SystemPermissions.INVITE_USERS,
        constants_1.SystemPermissions.CHANGE_USER_ROLES,
        constants_1.SystemPermissions.ASSIGN_USERS_DRS,
        constants_1.SystemPermissions.ACCESS_LEGAL_TEXT,
        constants_1.SystemPermissions.EXPORT_FINAL_REPORT,
        constants_1.SystemPermissions.EXPORT_DR_EXCEL,
        constants_1.SystemPermissions.EXPORT_DATAPOINTS_EXCEL,
    ],
    AI_CONTRIBUTOR: [
        constants_1.SystemPermissions.ACCESS_MATERIALITY_SETTING,
        constants_1.SystemPermissions.CREATE_DATAPOINTS,
        constants_1.SystemPermissions.EDIT_DATAPOINTS,
        constants_1.SystemPermissions.APPROVE_DATAPOINTS,
        constants_1.SystemPermissions.MARK_DATAPOINTS,
        constants_1.SystemPermissions.EXPORT_DATAPOINTS_EXCEL,
        constants_1.SystemPermissions.GAP_ANALYSIS_DATAPOINTS,
        constants_1.SystemPermissions.CREATE_DRS,
        constants_1.SystemPermissions.EDIT_DRS,
        constants_1.SystemPermissions.GAP_ANALYSIS_DRS,
        constants_1.SystemPermissions.APPROVE_DRS,
        constants_1.SystemPermissions.MARK_DRS,
        constants_1.SystemPermissions.ACCESS_VERSION_HISTORY,
        constants_1.SystemPermissions.RESTORE_VERSION_HISTORY,
        constants_1.SystemPermissions.CREATE_COMMENTS,
        constants_1.SystemPermissions.EDIT_COMMENTS,
        constants_1.SystemPermissions.ACCESS_LEGAL_TEXT,
        constants_1.SystemPermissions.EXPORT_FINAL_REPORT,
        constants_1.SystemPermissions.EXPORT_DR_EXCEL,
        constants_1.SystemPermissions.EXPORT_DATAPOINTS_EXCEL,
    ],
    CONTRIBUTOR: [
        constants_1.SystemPermissions.ACCESS_MATERIALITY_SETTING,
        constants_1.SystemPermissions.CREATE_COMMENTS,
        constants_1.SystemPermissions.ACCESS_LEGAL_TEXT,
    ],
};
class SchemaUpdate1750046830302 {
    constructor() {
        this.name = 'SchemaUpdate1750046830302';
    }
    getPermissionDescription(permission) {
        const descriptions = {
            [constants_1.SystemPermissions.CREATE_DATAPOINTS]: 'Create new datapoints',
            [constants_1.SystemPermissions.EDIT_DATAPOINTS]: 'Edit existing datapoints',
            [constants_1.SystemPermissions.APPROVE_DATAPOINTS]: 'Approve datapoints',
            [constants_1.SystemPermissions.MARK_DATAPOINTS]: 'Change status of datapoints',
            [constants_1.SystemPermissions.EXPORT_DATAPOINTS_EXCEL]: 'Export datapoints to Excel',
            [constants_1.SystemPermissions.CREATE_DRS]: 'Create new DRs',
            [constants_1.SystemPermissions.EDIT_DRS]: 'Edit existing DRs',
            [constants_1.SystemPermissions.APPROVE_DRS]: 'Approve DRs',
            [constants_1.SystemPermissions.MARK_DRS]: 'Change status of DRs',
            [constants_1.SystemPermissions.ASSIGN_USERS_DRS]: 'Assign users to DRs',
            [constants_1.SystemPermissions.EXPORT_DR_EXCEL]: 'Export DRs to Excel',
            [constants_1.SystemPermissions.GAP_ANALYSIS_DATAPOINTS]: 'Perform gap analysis on datapoints',
            [constants_1.SystemPermissions.GAP_ANALYSIS_DATAPOINTS_REVIEW]: 'Review datapoint gap analysis',
            [constants_1.SystemPermissions.GAP_ANALYSIS_DRS]: 'Perform gap analysis on DRs',
            [constants_1.SystemPermissions.GAP_ANALYSIS_DRS_REVIEW]: 'Review DR gap analysis',
            [constants_1.SystemPermissions.CREATE_COMMENTS]: 'Create comments',
            [constants_1.SystemPermissions.EDIT_COMMENTS]: 'Edit comments',
            [constants_1.SystemPermissions.CREATE_WORKSPACE]: 'Create new workspaces',
            [constants_1.SystemPermissions.SWITCH_WORKSPACE]: 'Switch between workspaces',
            [constants_1.SystemPermissions.EDIT_WORKSPACE_SETTINGS]: 'Edit workspace settings',
            [constants_1.SystemPermissions.EDIT_PROJECT_SETTINGS]: 'Edit project settings',
            [constants_1.SystemPermissions.CHANGE_USER_ROLES]: 'Modify user roles',
            [constants_1.SystemPermissions.INVITE_USERS]: 'Invite new users',
            [constants_1.SystemPermissions.ACCESS_LEGAL_TEXT]: 'Access legal text',
            [constants_1.SystemPermissions.EXPORT_FINAL_REPORT]: 'Export final reports',
            [constants_1.SystemPermissions.CHANGE_MATERIALITY_SETTING]: 'Modify materiality settings',
            [constants_1.SystemPermissions.ACCESS_MATERIALITY_SETTING]: 'View materiality settings',
            [constants_1.SystemPermissions.ACCESS_VERSION_HISTORY]: 'View version history',
            [constants_1.SystemPermissions.RESTORE_VERSION_HISTORY]: 'Restore version history entries',
        };
        return descriptions[permission] || `Permission ${permission}`;
    }
    async up(queryRunner) {
        const enumValues = Object.values(constants_1.SystemPermissions)
            .map((value) => `'${value}'`)
            .join(', ');
        await queryRunner.query(`CREATE TYPE "public"."permission_name_enum" AS ENUM (${enumValues})`);
        await queryRunner.query(`CREATE TABLE "permission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" "public"."permission_name_enum" NOT NULL, "description" character varying, CONSTRAINT "UQ_240853a0c3353c25fb12434ad33" UNIQUE ("name"), CONSTRAINT "PK_3b8b97af9d9d8807e41e6f48362" PRIMARY KEY ("id"))`);
        for (const permission of Object.values(constants_1.SystemPermissions)) {
            await queryRunner.query(`
        INSERT INTO "permission" (name, description)
        VALUES ($1, $2)
        ON CONFLICT (name) DO NOTHING
      `, [permission, this.getPermissionDescription(permission)]);
        }
        await queryRunner.query(`CREATE TABLE "role_permission" ("roleId" uuid NOT NULL, "permissionId" uuid NOT NULL, CONSTRAINT "PK_b42bbacb8402c353df822432544" PRIMARY KEY ("roleId", "permissionId"))`);
        await queryRunner.query(`ALTER TABLE "role_permission" ADD CONSTRAINT "FK_e3130a39c1e4a740d044e685730" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_permission" ADD CONSTRAINT "FK_72e80be86cab0e93e67ed1a7a9a" FOREIGN KEY ("permissionId") REFERENCES "permission"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        for (const [roleName, permissions] of Object.entries(RolePermissions)) {
            await queryRunner.query(`
        WITH role_data AS (
          SELECT id FROM "role" WHERE name = $1
        ),
        permission_data AS (
          SELECT id FROM "permission" WHERE name = ANY($2)
        )
        INSERT INTO "role_permission" ("roleId", "permissionId")
        SELECT rd.id, pd.id
        FROM role_data rd
        CROSS JOIN permission_data pd
      `, [roleName, permissions]);
        }
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "role_permission" DROP CONSTRAINT "FK_72e80be86cab0e93e67ed1a7a9a"`);
        await queryRunner.query(`ALTER TABLE "role_permission" DROP CONSTRAINT "FK_e3130a39c1e4a740d044e685730"`);
        await queryRunner.query(`DROP TABLE "role_permission"`);
        await queryRunner.query(`DROP TABLE "permission"`);
        await queryRunner.query(`DROP TYPE "public"."permission_name_enum"`);
    }
}
exports.SchemaUpdate1750046830302 = SchemaUpdate1750046830302;
//# sourceMappingURL=1750046830302-schema-update.js.map