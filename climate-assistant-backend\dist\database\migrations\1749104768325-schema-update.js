"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1749104768325 = void 0;
class SchemaUpdate1749104768325 {
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TYPE "role_name_enum" AS ENUM (
        'SUPER_ADMIN',
        'WORKSPACE_ADMIN',
        'AI_CONTRIBUTOR',
        'AI_ONLY_REVIEW',
        'CONTRIBUTOR'
      )
    `);
        await queryRunner.query(`
      CREATE TABLE "role" (
        "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
        "name" role_name_enum NOT NULL UNIQUE,
        "description" varchar
      )
    `);
        await queryRunner.query(`
      INSERT INTO "role" (name, description) VALUES
      ('SUPER_ADMIN', 'Super administrator with full access'),
      ('WORKSPACE_ADMIN', 'Workspace administrator'),
      ('AI_CONTRIBUTOR', 'AI content contributor'),
      ('AI_ONLY_REVIEW', 'AI content reviewer'),
      ('CONTRIBUTOR', 'Regular contributor')
    `);
        await queryRunner.query(`
      ALTER TABLE "user_workspace"
      ADD COLUMN "roleId" uuid
    `);
        await queryRunner.query(`
      UPDATE "user_workspace" uw
      SET "roleId" = r.id
      FROM "role" r
      WHERE r.name::text = uw.role::text
    `);
        await queryRunner.query(`
      ALTER TABLE "user_workspace"
      ADD CONSTRAINT "FK_user_workspace_role"
      FOREIGN KEY ("roleId")
      REFERENCES "role"(id)
    `);
        await queryRunner.query(`
      ALTER TABLE "user_workspace"
      DROP COLUMN "role"
    `);
        await queryRunner.query(`
      DROP TYPE IF EXISTS "public"."user_workspace_role_enum"
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      CREATE TYPE "user_workspace_role_enum" AS ENUM (
        'SUPER_ADMIN',
        'WORKSPACE_ADMIN',
        'AI_CONTRIBUTOR',
        'AI_ONLY_REVIEW',
        'CONTRIBUTOR'
      )
    `);
        await queryRunner.query(`
      ALTER TABLE "user_workspace"
      ADD COLUMN "role" "user_workspace_role_enum"
    `);
        await queryRunner.query(`
      UPDATE "user_workspace" uw
      SET role = r.name::"user_workspace_role_enum"
      FROM "role" r
      WHERE r.id = uw."roleId"
    `);
        await queryRunner.query(`
      ALTER TABLE "user_workspace"
      DROP CONSTRAINT "FK_user_workspace_role",
      DROP COLUMN "roleId"
    `);
        await queryRunner.query(`
      DROP TABLE "role"
    `);
        await queryRunner.query(`
      DROP TYPE "role_name_enum"
    `);
    }
}
exports.SchemaUpdate1749104768325 = SchemaUpdate1749104768325;
//# sourceMappingURL=1749104768325-schema-update.js.map