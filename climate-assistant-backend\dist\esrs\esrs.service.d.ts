import { DataSource, Repository } from 'typeorm';
import { LlmService } from '../llm/services/llm.service';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { ESRSDisclosureRequirement } from './entities/esrs-disclosure-requirement.entity';
import { ESRSTopic } from './entities/esrs-topic.entity';
export declare class EsrsService {
    private readonly esrsDatapointRepository;
    private readonly esrsDisclosureRequirementRepository;
    private readonly esrsTopicRepository;
    private dataSource;
    private readonly chatGptService;
    constructor(esrsDatapointRepository: Repository<ESRSDatapoint>, esrsDisclosureRequirementRepository: Repository<ESRSDisclosureRequirement>, esrsTopicRepository: Repository<ESRSTopic>, dataSource: DataSource, chatGptService: LlmService);
    getEsrsDatapointsByStandard(esrs: string): Promise<ESRSDatapoint[]>;
    getEsrsTopics(): Promise<ESRSTopic[]>;
}
