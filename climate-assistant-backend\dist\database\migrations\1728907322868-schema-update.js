"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1728907322868 = void 0;
class SchemaUpdate1728907322868 {
    constructor() {
        this.name = 'SchemaUpdate1728907322868';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "user_workspace" ALTER COLUMN "joinedAt" DROP NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "user_workspace" ALTER COLUMN "joinedAt" SET NOT NULL`);
    }
}
exports.SchemaUpdate1728907322868 = SchemaUpdate1728907322868;
//# sourceMappingURL=1728907322868-schema-update.js.map