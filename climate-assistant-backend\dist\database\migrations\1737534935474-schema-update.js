"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1737534935474 = void 0;
class SchemaUpdate1737534935474 {
    constructor() {
        this.name = 'SchemaUpdate1737534935474';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_generation" ADD "evaluatorId" uuid`);
        await queryRunner.query(`ALTER TABLE "datapoint_generation" ADD "evaluatedAt" TIMESTAMP`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_generation" DROP COLUMN "evaluatedAt"`);
        await queryRunner.query(`ALTER TABLE "datapoint_generation" DROP COLUMN "evaluatorId"`);
    }
}
exports.SchemaUpdate1737534935474 = SchemaUpdate1737534935474;
//# sourceMappingURL=1737534935474-schema-update.js.map