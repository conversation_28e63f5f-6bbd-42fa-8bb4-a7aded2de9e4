"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1730288906047 = void 0;
class SchemaUpdate1730288906047 {
    constructor() {
        this.name = 'SchemaUpdate1730288906047';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "comment" RENAME COLUMN "commentable_id" TO "commentableId"`);
        await queryRunner.query(`ALTER TABLE "comment" RENAME COLUMN "commentable_type" TO "commentableType"`);
        await queryRunner.query(`ALTER TYPE "comment_commentable_type_enum" RENAME TO "comment_commentabletype_enum"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "public"."IDX_b8d38847018497d8a6121b3abb"`);
        await queryRunner.query(`CREATE INDEX "IDX_b8d38847018497d8a6121b3abb" ON "comment" ("commentableId", "commentableType")`);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "public"."IDX_b8d38847018497d8a6121b3abb"`);
        await queryRunner.query(`ALTER TYPE "comment_commentabletype_enum" RENAME TO "comment_commentable_type_enum"`);
        await queryRunner.query(`ALTER TABLE "comment" RENAME COLUMN "commentableType" TO "commentable_type"`);
        await queryRunner.query(`ALTER TABLE "comment" RENAME COLUMN "commentableId" TO "commentable_id"`);
    }
}
exports.SchemaUpdate1730288906047 = SchemaUpdate1730288906047;
//# sourceMappingURL=1730288906047-schema-update.js.map