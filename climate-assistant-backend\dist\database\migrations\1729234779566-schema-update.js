"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1729234779566 = void 0;
class SchemaUpdate1729234779566 {
    constructor() {
        this.name = 'SchemaUpdate1729234779566';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ADD "esrsDisclosureRequirementId" integer`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ADD CONSTRAINT "FK_809f5e1cde50fe0978efc545ead" FOREIGN KEY ("esrsDisclosureRequirementId") REFERENCES "esrs_disclosure_requirement"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" DROP CONSTRAINT "FK_809f5e1cde50fe0978efc545ead"`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" DROP COLUMN "esrsDisclosureRequirementId"`);
    }
}
exports.SchemaUpdate1729234779566 = SchemaUpdate1729234779566;
//# sourceMappingURL=1729234779566-schema-update.js.map