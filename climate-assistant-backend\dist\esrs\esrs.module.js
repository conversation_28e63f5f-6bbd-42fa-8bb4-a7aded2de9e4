"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EsrsModule = void 0;
const common_1 = require("@nestjs/common");
const esrs_service_1 = require("./esrs.service");
const typeorm_1 = require("@nestjs/typeorm");
const llm_service_1 = require("../llm/services/llm.service");
const esrs_datapoint_entity_1 = require("../datapoint/entities/esrs-datapoint.entity");
const esrs_disclosure_requirement_entity_1 = require("./entities/esrs-disclosure-requirement.entity");
const esrs_topic_entity_1 = require("./entities/esrs-topic.entity");
let EsrsModule = class EsrsModule {
};
exports.EsrsModule = EsrsModule;
exports.EsrsModule = EsrsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                esrs_topic_entity_1.ESRSTopic,
                esrs_datapoint_entity_1.ESRSDatapoint,
                esrs_disclosure_requirement_entity_1.ESRSDisclosureRequirement,
            ]),
        ],
        providers: [esrs_service_1.EsrsService, llm_service_1.LlmService],
        exports: [esrs_service_1.EsrsService],
    })
], EsrsModule);
//# sourceMappingURL=esrs.module.js.map