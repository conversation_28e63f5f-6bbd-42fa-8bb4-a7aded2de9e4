import { ESRSTopicDisclosureRequirement } from './esrs_topic_disclosure_requirement.entity';
import { ESRSDatapoint } from '../../datapoint/entities/esrs-datapoint.entity';
import { DataRequest } from '../../data-request/entities/data-request.entity';
export declare class ESRSDisclosureRequirement {
    id: number;
    sort: number;
    dr: string;
    esrs: string;
    name: string;
    drDescription?: string;
    drObjective?: string;
    lawText?: string;
    lawTextAR?: string;
    publicAccess: boolean;
    createdAt: Date;
    updatedAt: Date;
    topicRelations: ESRSTopicDisclosureRequirement[];
    esrsDatapoints: ESRSDatapoint[];
    dataRequests: DataRequest[];
}
