"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1737540087061 = void 0;
class SchemaUpdate1737540087061 {
    constructor() {
        this.name = 'SchemaUpdate1737540087061';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "comment_generation" DROP COLUMN "resolved"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9b2369479bbdd96d6a446437a4"`);
        await queryRunner.query(`ALTER TABLE "comment_generation" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "comment_generation" ALTER COLUMN "status" TYPE TEXT USING "status"::TEXT`);
        await queryRunner.query(`UPDATE "comment_generation" SET "status" = 'pending' WHERE "status" IN ('under_approval', 'unchecked') `);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."comment_generation_status_enum"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."comment_generation_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."comment_generation_status_enum"  AS ENUM('pending', 'approved', 'rejected')`);
        await queryRunner.query(`ALTER TABLE "comment_generation" ALTER COLUMN "status" TYPE "public"."comment_generation_status_enum" USING "status"::TEXT::"public"."comment_generation_status_enum"`);
        await queryRunner.query(`ALTER TABLE "comment_generation" ALTER COLUMN "status" SET DEFAULT 'pending'`);
        await queryRunner.query(`CREATE INDEX "IDX_9b2369479bbdd96d6a446437a4" ON "comment_generation" ("commentableId", "commentableType")`);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "public"."IDX_9b2369479bbdd96d6a446437a4"`);
        await queryRunner.query(`ALTER TABLE "comment_generation" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "comment_generation" ALTER COLUMN "status" TYPE TEXT USING "status"::TEXT`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."comment_generation_status_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."comment_generation_status_enum" AS ENUM('under_approval', 'approved', 'rejected', 'unchecked')`);
        await queryRunner.query(`ALTER TABLE "comment_generation" ALTER COLUMN "status" TYPE "public"."comment_generation_status_enum" USING "status"::TEXT::"public"."comment_generation_status_enum"`);
        await queryRunner.query(`ALTER TABLE "comment_generation" ALTER COLUMN "status" SET DEFAULT 'unchecked'`);
        await queryRunner.query(`CREATE INDEX "IDX_9b2369479bbdd96d6a446437a4" ON "comment_generation" ("commentableId", "commentableType")`);
        await queryRunner.query(`ALTER TABLE "comment_generation" ADD "resolved" BOOLEAN NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "comment_generation" ALTER COLUMN "resolved" DROP DEFAULT`);
    }
}
exports.SchemaUpdate1737540087061 = SchemaUpdate1737540087061;
//# sourceMappingURL=1737540087061-schema-update.js.map