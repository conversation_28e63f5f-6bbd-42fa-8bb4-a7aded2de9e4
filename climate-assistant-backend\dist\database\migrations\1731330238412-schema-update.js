"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1731330238412 = void 0;
class SchemaUpdate1731330238412 {
    constructor() {
        this.name = 'SchemaUpdate1731330238412';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD "key_information" text`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP COLUMN "key_information"`);
    }
}
exports.SchemaUpdate1731330238412 = SchemaUpdate1731330238412;
//# sourceMappingURL=1731330238412-schema-update.js.map