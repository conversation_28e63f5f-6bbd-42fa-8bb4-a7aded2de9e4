{"version": 3, "file": "1728642599474-schema-update.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/1728642599474-schema-update.ts"], "names": [], "mappings": ";;;AAEA,MAAa,yBAAyB;IAAtC;QACE,SAAI,GAAG,2BAA2B,CAAC;IA2PrC,CAAC;IAzPQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,oFAAoF,CACrF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mQAAmQ,CACpQ,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qJAAqJ,CACtJ,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mUAAmU,CACpU,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6LAA6L,CAC9L,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,gFAAgF,CACjF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qVAAqV,CACtV,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0RAA0R,CAC3R,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6NAA6N,CAC9N,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mLAAmL,CACpL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mMAAmM,CACpM,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6HAA6H,CAC9H,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0WAA0W,CAC3W,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oJAAoJ,CACrJ,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,meAAme,CACpe,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mGAAmG,CACpG,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4VAA4V,CAC7V,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+QAA+Q,CAChR,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0TAA0T,CAC3T,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8OAA8O,CAC/O,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6MAA6M,CAC9M,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2OAA2O,CAC5O,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uGAAuG,CACxG,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uHAAuH,CACxH,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;QACxE,MAAM,WAAW,CAAC,KAAK,CACrB,sDAAsD,CACvD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qEAAqE,CACtE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4JAA4J,CAC7J,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,gLAAgL,CACjL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qLAAqL,CACtL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wKAAwK,CACzK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iKAAiK,CAClK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yKAAyK,CAC1K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6KAA6K,CAC9K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wMAAwM,CACzM,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,kMAAkM,CACnM,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uLAAuL,CACxL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2LAA2L,CAC5L,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qMAAqM,CACtM,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,gLAAgL,CACjL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yKAAyK,CAC1K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8JAA8J,CAC/J,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qKAAqK,CACtK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+KAA+K,CAChL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wKAAwK,CACzK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,gLAAgL,CACjL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+LAA+L,CAChM,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oOAAoO,CACrO,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,kGAAkG,CACnG,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,kGAAkG,CACnG,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,gFAAgF,CACjF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+EAA+E,CAChF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+EAA+E,CAChF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,kFAAkF,CACnF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,kFAAkF,CACnF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yFAAyF,CAC1F,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yFAAyF,CAC1F,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+EAA+E,CAChF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oFAAoF,CACrF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oFAAoF,CACrF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sEAAsE,CACvE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;QACtE,MAAM,WAAW,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;QACjE,MAAM,WAAW,CAAC,KAAK,CACrB,4EAA4E,CAC7E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sDAAsD,CACvD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sDAAsD,CACvD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAC1E,MAAM,WAAW,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAClD,MAAM,WAAW,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;QACxD,MAAM,WAAW,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAChD,MAAM,WAAW,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACvD,MAAM,WAAW,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAChD,MAAM,WAAW,CAAC,KAAK,CACrB,oDAAoD,CACrD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;QACrD,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;QAC1D,MAAM,WAAW,CAAC,KAAK,CACrB,oDAAoD,CACrD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACvD,MAAM,WAAW,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;QACjE,MAAM,WAAW,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACvD,MAAM,WAAW,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QACjD,MAAM,WAAW,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAChD,MAAM,WAAW,CAAC,KAAK,CACrB,0DAA0D,CAC3D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;QAC5D,MAAM,WAAW,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;QACpE,MAAM,WAAW,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QACnD,MAAM,WAAW,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAC9C,MAAM,WAAW,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;IAClE,CAAC;CACF;AA5PD,8DA4PC"}