"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1729272658507 = void 0;
class SchemaUpdate1729272658507 {
    constructor() {
        this.name = 'SchemaUpdate1729272658507';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document_chunk" ALTER COLUMN "matchingsJson" DROP NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document_chunk" ALTER COLUMN "matchingsJson" SET NOT NULL`);
    }
}
exports.SchemaUpdate1729272658507 = SchemaUpdate1729272658507;
//# sourceMappingURL=1729272658507-schema-update.js.map