"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1738128645630 = void 0;
class SchemaUpdate1738128645630 {
    constructor() {
        this.name = 'SchemaUpdate1738128645630';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ADD "exampleOutput" text`);
        await queryRunner.query(`UPDATE esrs_datapoint
        SET "exampleOutput" = '| **Product Group** | **Product/Material Name** | **Expected Durability (Years)** | **Industry Average Durability (Years)** | **Deviation (Years)** | **Deviation (%)** | **Packaging Included (Yes/No)** | **Notes/Comments** |
        | ----------------- | ------------------------- | ------------------------------- | --------------------------------------- | --------------------- | ----------------- | ------------------------------- | ------------------ |
        |                   |                           |                                 |                                         |                       |                   |                                 |                    |
        |                   |                           |                                 |                                         |                       |                   |                                 |                    |
        |                   |                           |                                 |                                         |                       |                   |                                 |                    |
        |                   |                           |                                 |                                         |                       |                   |                                 |                    |
        |                   |                           |                                 |                                         |                       |                   |                                 |                    |
        |                   |                           |                                 |                                         |                       |                   |                                 |                    |'
        WHERE "datapointId" = 'E5-5_02';`);
        await queryRunner.query(`UPDATE esrs_datapoint
        SET "exampleOutput" = '| **Recovery Operation Type** | **Total Amount Diverted (kg/tonnes)** | **Hazardous Waste (kg/tonnes)** | **Non-Hazardous Waste (kg/tonnes)** | **Notes/Comments** |
        | --------------------------- | ------------------------------------- | ------------------------------- | ----------------------------------- | ------------------ |
        | Preparation for Reuse       |                                       |                                 |                                     |                    |
        | Recycling                   |                                       |                                 |                                     |                    |
        | Other Recovery Operations   |                                       |                                 |                                     |                    |
        | **Total**                   |                                       |                                 |                                     |                    |'
        WHERE "datapointId" = 'E5-5_08';`);
        await queryRunner.query(`UPDATE esrs_datapoint
        SET "exampleOutput" = '| **Disposal Treatment Type** | **Total Amount (kg/tonnes)** | **Hazardous Waste (kg/tonnes)** | **Non-Hazardous Waste (kg/tonnes)** | **Notes/Comments** |
        | --------------------------- | ---------------------------- | ------------------------------- | ----------------------------------- | ------------------ |
        | Incineration                |                              |                                 |                                     |                    |
        | Landfill                    |                              |                                 |                                     |                    |
        | Other Disposal Operations   |                              |                                 |                                     |                    |
        | **Total**                   |                              |                                 |                                     |                    |'
        WHERE "datapointId" = 'E5-5_09';`);
        await queryRunner.query(`UPDATE esrs_datapoint
        SET "exampleOutput" = '|                       | At-risk functions          | Managers                   | AMSB (^132^)              | Other own workers         |
|-----------------------|----------------------------|----------------------------|----------------------------|----------------------------|
| **Training coverage** |                            |                            |                            |                            |
| Total                 |                            |                            |                            |                            |
| Total receiving training |                        |                            |                            |                            |
| **Delivery method and duration** |               |                            |                            |                            |
| Classroom training    |                            |                            |                            |                            |
| Computer-based training |                         |                            |                            |                            |
| Voluntary computer-based training |              |                            |                            |                            |
| **Frequency**         |                            |                            |                            |                            |
| How often training is required |                  |                            |                            | -                          |
| **Topics covered**    |                            |                            |                            |                            |
| Definition of corruption |                        |                            |                            |                            |
| Policy                |                            |                            |                            |                            |
| Procedures on suspicion/detection |               |                            |                            |                            |
| Etc.                  |                            |                            |                            |                            |'
        WHERE "datapointId" = 'G1-4_03';`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" DROP COLUMN "exampleOutput"`);
    }
}
exports.SchemaUpdate1738128645630 = SchemaUpdate1738128645630;
//# sourceMappingURL=1738128645630-schema-update.js.map