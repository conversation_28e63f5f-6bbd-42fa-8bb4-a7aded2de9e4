import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ESRSTopicDisclosureRequirement } from './esrs_topic_disclosure_requirement.entity';
import { ESRSTopicDatapoint } from './esrs-topic-datapoint.entity';
import { MaterialESRSTopic } from '../../project/entities/material-esrs-topic.entity';

export enum ESRSTopicLevel {
  TOPIC = 'topic',
  SUB_TOPIC = 'sub-topic',
  SUB_SUB_TOPIC = 'sub-sub-topic',
}

@Entity()
export class ESRSTopic {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar' })
  name: string;

  @Column({
    type: 'enum',
    enum: ESRSTopicLevel,
    default: ESRSTopicLevel.TOPIC,
  })
  level: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'integer' })
  parentId: number;

  @ManyToOne(() => ESRSTopic, (parent) => parent.children, { nullable: true })
  @JoinColumn({ name: 'parentId' })
  parent: ESRSTopic | null;

  @OneToMany(() => ESRSTopic, (child) => child.parent)
  children: ESRSTopic[];

  @OneToMany(() => ESRSTopicDisclosureRequirement, (relation) => relation.topic)
  disclosureRequirementRelations: ESRSTopicDisclosureRequirement[];

  @OneToMany(() => ESRSTopicDatapoint, (relation) => relation.topic)
  datapointRelations: ESRSTopicDatapoint[];

  @OneToMany(
    () => MaterialESRSTopic,
    (materialESRSTopic) => materialESRSTopic.esrsTopic,
  )
  materialTopics: MaterialESRSTopic[];
}
