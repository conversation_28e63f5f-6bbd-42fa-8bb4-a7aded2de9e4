"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ESRSTopicDatapoint = void 0;
const typeorm_1 = require("typeorm");
const esrs_topic_entity_1 = require("./esrs-topic.entity");
const esrs_datapoint_entity_1 = require("../../datapoint/entities/esrs-datapoint.entity");
let ESRSTopicDatapoint = class ESRSTopicDatapoint {
};
exports.ESRSTopicDatapoint = ESRSTopicDatapoint;
__decorate([
    (0, typeorm_1.PrimaryColumn)(),
    __metadata("design:type", Number)
], ESRSTopicDatapoint.prototype, "esrsTopicId", void 0);
__decorate([
    (0, typeorm_1.PrimaryColumn)(),
    __metadata("design:type", Number)
], ESRSTopicDatapoint.prototype, "esrsDatapointId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => esrs_topic_entity_1.ESRSTopic, (topic) => topic.datapointRelations),
    (0, typeorm_1.JoinColumn)({ name: 'esrsTopicId' }),
    __metadata("design:type", esrs_topic_entity_1.ESRSTopic)
], ESRSTopicDatapoint.prototype, "topic", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => esrs_datapoint_entity_1.ESRSDatapoint, (dr) => dr.topicRelations),
    (0, typeorm_1.JoinColumn)({ name: 'esrsDatapointId' }),
    __metadata("design:type", esrs_datapoint_entity_1.ESRSDatapoint)
], ESRSTopicDatapoint.prototype, "datapoint", void 0);
exports.ESRSTopicDatapoint = ESRSTopicDatapoint = __decorate([
    (0, typeorm_1.Entity)('esrs_topic_datapoint')
], ESRSTopicDatapoint);
//# sourceMappingURL=esrs-topic-datapoint.entity.js.map