"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1738173452082 = void 0;
class SchemaUpdate1738173452082 {
    constructor() {
        this.name = 'SchemaUpdate1738173452082';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_request" DROP COLUMN "publicAccess"`);
        await queryRunner.query(`ALTER TABLE "data_request" DROP COLUMN "publicAccess"`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ADD "publicAccess" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "esrs_disclosure_requirement" ADD "publicAccess" boolean NOT NULL DEFAULT false`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_disclosure_requirement" DROP COLUMN "publicAccess"`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" DROP COLUMN "publicAccess"`);
        await queryRunner.query(`ALTER TABLE "data_request" ADD "publicAccess" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "datapoint_request" ADD "publicAccess" boolean NOT NULL DEFAULT false`);
    }
}
exports.SchemaUpdate1738173452082 = SchemaUpdate1738173452082;
//# sourceMappingURL=1738173452082-schema-update.js.map