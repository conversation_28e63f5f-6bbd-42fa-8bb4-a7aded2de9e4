"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1732207882402 = void 0;
class SchemaUpdate1732207882402 {
    async up(queryRunner) {
        await queryRunner.query(`DELETE FROM esrs_topic WHERE id > 11;`);
        await queryRunner.query(`INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (12, 'Climate change adaptation', 'sub-topic', 1);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (13, 'Climate change mitigation', 'sub-topic', 1);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (14, 'Energy', 'sub-topic', 1);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (15, 'Pollution of air', 'sub-topic', 2);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (16, 'Pollution of water', 'sub-topic', 2);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (17, 'Pollution of soil', 'sub-topic', 2);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (18, 'Pollution of living organisms and food resources', 'sub-topic', 2);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (19, 'Substances of concern', 'sub-topic', 2);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (20, 'Substances of very high concern', 'sub-topic', 2);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (21, 'Microplastics', 'sub-topic', 2);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (22, 'Water', 'sub-topic', 3);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (23, 'Marine resources', 'sub-topic', 3);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (24, 'Direct impact drivers of biodiversity loss', 'sub-topic', 4);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (25, 'Impacts on the state of species', 'sub-topic', 4);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (26, 'Impacts on the extent and condition of ecosystems', 'sub-topic', 4);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (27, 'Impacts and dependencies on ecosystem services', 'sub-topic', 4);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (28, 'Resources inflows, including resource use', 'sub-topic', 5);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (29, 'Resource outflows related to products and services', 'sub-topic', 5);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (30, 'Waste', 'sub-topic', 5);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (31, 'Working conditions', 'sub-topic', 6);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (32, 'Equal treatment and opportunities for all', 'sub-topic', 6);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (33, 'Other work-related rights', 'sub-topic', 6);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (34, 'Working conditions', 'sub-topic', 7);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (35, 'Equal treatment and opportunities for all', 'sub-topic', 7);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (36, 'Other work-related rights', 'sub-topic', 7);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (37, 'Communities economic, social and cultural rights', 'sub-topic', 8);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (38, 'Communities civil and political rights', 'sub-topic', 8);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (39, 'Rights of indigenous peoples', 'sub-topic', 8);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (40, 'Information-related impacts for consumers and/or end-users', 'sub-topic', 9);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (41, 'Personal safety of consumers and/or end-users', 'sub-topic', 9);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (42, 'Social inclusion of consumers and/or end-users', 'sub-topic', 9);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (43, 'Corporate culture', 'sub-topic', 10);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (44, 'Protection of whistle-blowers', 'sub-topic', 10);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (45, 'Animal welfare', 'sub-topic', 10);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (46, 'Political engagement and lobbying activities', 'sub-topic', 10);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (47, 'Management of relationships with suppliers including payment practices', 'sub-topic', 10);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (48, 'Corruption and bribery', 'sub-topic', 10);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (49, 'Water consumption', 'sub-sub-topic', 22);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (50, 'Water withdrawals', 'sub-sub-topic', 22);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (51, 'Water discharges', 'sub-sub-topic', 22);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (52, 'Water discharges in the oceans', 'sub-sub-topic', 22);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (53, 'Extraction and use of marine resources', 'sub-sub-topic', 22);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (54, 'Water consumption', 'sub-sub-topic', 23);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (55, 'Water withdrawals', 'sub-sub-topic', 23);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (56, 'Water discharges', 'sub-sub-topic', 23);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (57, 'Water discharges in the oceans', 'sub-sub-topic', 23);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (58, 'Extraction and use of marine resources', 'sub-sub-topic', 23);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (59, 'Climate Change', 'sub-sub-topic', 24);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (60, 'Land-use change, fresh water-use change and sea-use change', 'sub-sub-topic', 24);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (61, 'Direct exploitation', 'sub-sub-topic', 24);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (62, 'Invasive alien species', 'sub-sub-topic', 24);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (63, 'Pollution', 'sub-sub-topic', 24);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (64, 'Others', 'sub-sub-topic', 24);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (65, 'Species population size', 'sub-sub-topic', 25);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (66, 'Species global extinction risk', 'sub-sub-topic', 25);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (67, 'Land degradation', 'sub-sub-topic', 26);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (68, 'Desertification', 'sub-sub-topic', 26);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (69, 'Soil sealing', 'sub-sub-topic', 26);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (70, 'Secure employment', 'sub-sub-topic', 31);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (71, 'Working time', 'sub-sub-topic', 31);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (72, 'Adequate wages', 'sub-sub-topic', 31);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (73, 'Social dialogue', 'sub-sub-topic', 31);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (74, 'Freedom of association, the existence of works councils and the information, consultation and participation rights of workers', 'sub-sub-topic', 31);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (75, 'Collective bargaining, including rate of workers covered by collective agreements', 'sub-sub-topic', 31);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (76, 'Work-life balance', 'sub-sub-topic', 31);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (77, 'Health and safety', 'sub-sub-topic', 31);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (78, 'Gender equality and equal pay for work of equal value', 'sub-sub-topic', 32);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (79, 'Training and skills development', 'sub-sub-topic', 32);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (80, 'Employment and inclusion of persons with disabilities', 'sub-sub-topic', 32);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (81, 'Measures against violence and harassment in the workplace', 'sub-sub-topic', 32);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (82, 'Diversity', 'sub-sub-topic', 32);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (83, 'Child labour', 'sub-sub-topic', 33);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (84, 'Forced labour', 'sub-sub-topic', 33);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (85, 'Adequate housing', 'sub-sub-topic', 33);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (86, 'Privacy', 'sub-sub-topic', 33);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (87, 'Secure employment', 'sub-sub-topic', 34);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (88, 'Working time', 'sub-sub-topic', 34);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (89, 'Adequate wages', 'sub-sub-topic', 34);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (90, 'Social dialogue', 'sub-sub-topic', 34);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (91, 'Freedom of association, including the existence of work councils', 'sub-sub-topic', 34);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (92, 'Collective bargaining', 'sub-sub-topic', 34);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (93, 'Work-life balance', 'sub-sub-topic', 34);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (94, 'Health and safety', 'sub-sub-topic', 34);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (95, 'Gender equality and equal pay for work of equal value', 'sub-sub-topic', 35);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (96, 'Training and skills development', 'sub-sub-topic', 35);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (97, 'The employment and inclusion of persons with disabilities', 'sub-sub-topic', 35);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (98, 'Measures against violence and harassment in the workplace', 'sub-sub-topic', 35);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (99, 'Diversity', 'sub-sub-topic', 35);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (100, 'Child labour', 'sub-sub-topic', 36);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (101, 'Forced labour', 'sub-sub-topic', 36);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (102, 'Adequate housing', 'sub-sub-topic', 36);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (103, 'Water and sanitation', 'sub-sub-topic', 36);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (104, 'Privacy', 'sub-sub-topic', 36);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (105, 'Adequate housing', 'sub-sub-topic', 37);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (106, 'Adequate food', 'sub-sub-topic', 37);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (107, 'Water and sanitation', 'sub-sub-topic', 37);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (108, 'Land-related impacts', 'sub-sub-topic', 37);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (109, 'Security-related impacts', 'sub-sub-topic', 37);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (110, 'Freedom of expression', 'sub-sub-topic', 38);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (111, 'Freedom of assembly', 'sub-sub-topic', 38);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (112, 'Impacts on human rights defenders', 'sub-sub-topic', 38);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (113, 'Free, prior and informed consent', 'sub-sub-topic', 39);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (114, 'Self-determination', 'sub-sub-topic', 39);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (115, 'Cultural rights', 'sub-sub-topic', 39);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (116, 'Privacy', 'sub-sub-topic', 40);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (117, 'Freedom of expression', 'sub-sub-topic', 40);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (118, 'Access to (quality) information', 'sub-sub-topic', 40);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (119, 'Health and safety', 'sub-sub-topic', 41);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (120, 'Security of person', 'sub-sub-topic', 41);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (121, 'Protection of children', 'sub-sub-topic', 41);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (122, 'Non-discrimination', 'sub-sub-topic', 42);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (123, 'Access to products and services', 'sub-sub-topic', 42);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (124, 'Responsible marketing practices', 'sub-sub-topic', 42);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (125, 'Prevention and detection including training', 'sub-sub-topic', 48);
        INSERT INTO public.esrs_topic (id, name, level, "parentId") VALUES (126, 'Incidents', 'sub-sub-topic', 48);`);
    }
    async down(queryRunner) {
        await queryRunner.query(`DELETE FROM esrs_topic WHERE id > 11;`);
    }
}
exports.SchemaUpdate1732207882402 = SchemaUpdate1732207882402;
//# sourceMappingURL=1732207882402-schema-update.js.map