"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1730961026458 = void 0;
class SchemaUpdate1730961026458 {
    constructor() {
        this.name = 'SchemaUpdate1730961026458';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE datapoint_document_chunk
      DROP CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d",
      ADD CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d"
      FOREIGN KEY ("documentChunkId") REFERENCES document_chunk("id") ON DELETE CASCADE`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE datapoint_document_chunk
      DROP CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d",
      ADD CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d"
      FOREIGN KEY ("documentChunkId") REFERENCES document_chunk("id") ON DELETE NO ACTION;`);
    }
}
exports.SchemaUpdate1730961026458 = SchemaUpdate1730961026458;
//# sourceMappingURL=1730961026458-schema-update.js.map