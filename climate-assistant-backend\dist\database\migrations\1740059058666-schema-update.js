"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1740059058666 = void 0;
class SchemaUpdate1740059058666 {
    constructor() {
        this.name = 'SchemaUpdate1740059058666';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TYPE "public"."datapoint_generation_status_enum" RENAME TO "datapoint_generation_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."datapoint_generation_status_enum" AS ENUM('pending', 'approved', 'rejected', 'minorChanges')`);
        await queryRunner.query(`ALTER TABLE "datapoint_generation" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "datapoint_generation" ALTER COLUMN "status" TYPE "public"."datapoint_generation_status_enum" USING "status"::"text"::"public"."datapoint_generation_status_enum"`);
        await queryRunner.query(`ALTER TABLE "datapoint_generation" ALTER COLUMN "status" SET DEFAULT 'pending'`);
        await queryRunner.query(`DROP TYPE "public"."datapoint_generation_status_enum_old"`);
    }
    async down(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."datapoint_generation_status_enum_old" AS ENUM('pending', 'approved', 'rejected')`);
        await queryRunner.query(`ALTER TABLE "datapoint_generation" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "datapoint_generation" ALTER COLUMN "status" TYPE "public"."datapoint_generation_status_enum_old" USING "status"::"text"::"public"."datapoint_generation_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "datapoint_generation" ALTER COLUMN "status" SET DEFAULT 'pending'`);
        await queryRunner.query(`DROP TYPE "public"."datapoint_generation_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."datapoint_generation_status_enum_old" RENAME TO "datapoint_generation_status_enum"`);
    }
}
exports.SchemaUpdate1740059058666 = SchemaUpdate1740059058666;
//# sourceMappingURL=1740059058666-schema-update.js.map