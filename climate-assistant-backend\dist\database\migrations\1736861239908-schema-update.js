"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1736861239908 = void 0;
class SchemaUpdate1736861239908 {
    constructor() {
        this.name = 'SchemaUpdate1736861239908';
    }
    async up(queryRunner) {
        await queryRunner.query(`WITH cte AS (
        SELECT
            id,
            ROW_NUMBER() OVER (
                PARTITION BY "documentChunkId", "datapointRequestId"
                ORDER BY id
            ) AS rn
        FROM "datapoint_document_chunk"
      )
      DELETE FROM "datapoint_document_chunk" t
      USING cte
      WHERE t.id = cte.id
      AND cte.rn > 1;`);
        await queryRunner.query(`WITH Deduped AS (
            SELECT
                id,
                "documentId",
                content,
                ROW_NUMBER() OVER (PARTITION BY "documentId", content ORDER BY id) AS row_num
            FROM document_chunk
        )
        DELETE FROM document_chunk
        WHERE id IN (
            SELECT id
            FROM Deduped
            WHERE row_num > 1
        );`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD CONSTRAINT "UQ_f35efcf411d58fd6f5a089621f3" UNIQUE ("documentChunkId", "datapointRequestId")`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP CONSTRAINT "UQ_f35efcf411d58fd6f5a089621f3"`);
    }
}
exports.SchemaUpdate1736861239908 = SchemaUpdate1736861239908;
//# sourceMappingURL=1736861239908-schema-update.js.map