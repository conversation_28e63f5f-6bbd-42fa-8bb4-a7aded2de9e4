"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ESRSDisclosureRequirement = void 0;
const typeorm_1 = require("typeorm");
const esrs_topic_disclosure_requirement_entity_1 = require("./esrs_topic_disclosure_requirement.entity");
const esrs_datapoint_entity_1 = require("../../datapoint/entities/esrs-datapoint.entity");
const data_request_entity_1 = require("../../data-request/entities/data-request.entity");
let ESRSDisclosureRequirement = class ESRSDisclosureRequirement {
};
exports.ESRSDisclosureRequirement = ESRSDisclosureRequirement;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], ESRSDisclosureRequirement.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], ESRSDisclosureRequirement.prototype, "sort", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], ESRSDisclosureRequirement.prototype, "dr", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], ESRSDisclosureRequirement.prototype, "esrs", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], ESRSDisclosureRequirement.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ESRSDisclosureRequirement.prototype, "drDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ESRSDisclosureRequirement.prototype, "drObjective", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ESRSDisclosureRequirement.prototype, "lawText", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ESRSDisclosureRequirement.prototype, "lawTextAR", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], ESRSDisclosureRequirement.prototype, "publicAccess", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ESRSDisclosureRequirement.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ESRSDisclosureRequirement.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => esrs_topic_disclosure_requirement_entity_1.ESRSTopicDisclosureRequirement, (relation) => relation.disclosureRequirement),
    __metadata("design:type", Array)
], ESRSDisclosureRequirement.prototype, "topicRelations", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => esrs_datapoint_entity_1.ESRSDatapoint, (esrsDatapoint) => esrsDatapoint.esrsDisclosureRequirement),
    __metadata("design:type", Array)
], ESRSDisclosureRequirement.prototype, "esrsDatapoints", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => data_request_entity_1.DataRequest, (dataRequest) => dataRequest.disclosureRequirement),
    __metadata("design:type", Array)
], ESRSDisclosureRequirement.prototype, "dataRequests", void 0);
exports.ESRSDisclosureRequirement = ESRSDisclosureRequirement = __decorate([
    (0, typeorm_1.Entity)()
], ESRSDisclosureRequirement);
//# sourceMappingURL=esrs-disclosure-requirement.entity.js.map