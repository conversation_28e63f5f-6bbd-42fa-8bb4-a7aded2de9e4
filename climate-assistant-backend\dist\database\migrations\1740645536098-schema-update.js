"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1740645536098 = void 0;
class SchemaUpdate1740645536098 {
    constructor() {
        this.name = 'SchemaUpdate1740645536098';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "workspace" ADD "reportTextGenerationRules" text`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "workspace" DROP COLUMN "reportTextGenerationRules"`);
    }
}
exports.SchemaUpdate1740645536098 = SchemaUpdate1740645536098;
//# sourceMappingURL=1740645536098-schema-update.js.map