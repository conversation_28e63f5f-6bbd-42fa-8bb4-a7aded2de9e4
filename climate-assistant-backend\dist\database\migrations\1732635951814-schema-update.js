"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1732635951814 = void 0;
class SchemaUpdate1732635951814 {
    constructor() {
        this.name = 'SchemaUpdate1732635951814';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "material_esrs_topic" ADD "active" boolean NOT NULL DEFAULT true`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "material_esrs_topic" DROP COLUMN "active"`);
    }
}
exports.SchemaUpdate1732635951814 = SchemaUpdate1732635951814;
//# sourceMappingURL=1732635951814-schema-update.js.map