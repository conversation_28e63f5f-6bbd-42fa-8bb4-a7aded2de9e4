"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ESRSTopic = exports.ESRSTopicLevel = void 0;
const typeorm_1 = require("typeorm");
const esrs_topic_disclosure_requirement_entity_1 = require("./esrs_topic_disclosure_requirement.entity");
const esrs_topic_datapoint_entity_1 = require("./esrs-topic-datapoint.entity");
const material_esrs_topic_entity_1 = require("../../project/entities/material-esrs-topic.entity");
var ESRSTopicLevel;
(function (ESRSTopicLevel) {
    ESRSTopicLevel["TOPIC"] = "topic";
    ESRSTopicLevel["SUB_TOPIC"] = "sub-topic";
    ESRSTopicLevel["SUB_SUB_TOPIC"] = "sub-sub-topic";
})(ESRSTopicLevel || (exports.ESRSTopicLevel = ESRSTopicLevel = {}));
let ESRSTopic = class ESRSTopic {
};
exports.ESRSTopic = ESRSTopic;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], ESRSTopic.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], ESRSTopic.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ESRSTopicLevel,
        default: ESRSTopicLevel.TOPIC,
    }),
    __metadata("design:type", String)
], ESRSTopic.prototype, "level", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ESRSTopic.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer' }),
    __metadata("design:type", Number)
], ESRSTopic.prototype, "parentId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ESRSTopic, (parent) => parent.children, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'parentId' }),
    __metadata("design:type", ESRSTopic)
], ESRSTopic.prototype, "parent", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ESRSTopic, (child) => child.parent),
    __metadata("design:type", Array)
], ESRSTopic.prototype, "children", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => esrs_topic_disclosure_requirement_entity_1.ESRSTopicDisclosureRequirement, (relation) => relation.topic),
    __metadata("design:type", Array)
], ESRSTopic.prototype, "disclosureRequirementRelations", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => esrs_topic_datapoint_entity_1.ESRSTopicDatapoint, (relation) => relation.topic),
    __metadata("design:type", Array)
], ESRSTopic.prototype, "datapointRelations", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => material_esrs_topic_entity_1.MaterialESRSTopic, (materialESRSTopic) => materialESRSTopic.esrsTopic),
    __metadata("design:type", Array)
], ESRSTopic.prototype, "materialTopics", void 0);
exports.ESRSTopic = ESRSTopic = __decorate([
    (0, typeorm_1.Entity)()
], ESRSTopic);
//# sourceMappingURL=esrs-topic.entity.js.map