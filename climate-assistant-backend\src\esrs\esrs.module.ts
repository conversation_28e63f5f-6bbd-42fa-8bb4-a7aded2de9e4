import { Module } from '@nestjs/common';
import { EsrsService } from './esrs.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LlmService } from '../llm/services/llm.service';
import { ESRSDatapoint } from '../datapoint/entities/esrs-datapoint.entity';
import { ESRSDisclosureRequirement } from './entities/esrs-disclosure-requirement.entity';
import { ESRSTopic } from './entities/esrs-topic.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ESRSTopic,
      ESRSDatapoint,
      ESRSDisclosureRequirement,
    ]),
  ],
  providers: [EsrsService, LlmService],
  exports: [EsrsService],
})
export class EsrsModule {}
