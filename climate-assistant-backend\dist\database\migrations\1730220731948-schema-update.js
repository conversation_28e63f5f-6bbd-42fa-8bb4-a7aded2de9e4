"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1730220731948 = void 0;
class SchemaUpdate1730220731948 {
    async up(queryRunner) {
        await queryRunner.query(`
        UPDATE user_workspace set role = 'CONTRIBUTOR' from "user" where "user"."id"= "user_workspace"."userId" and "name" <> 'Glacier AI';;
        UPDATE user_workspace set role = 'SUPER_ADMIN' from "user" where "user"."id"= "user_workspace"."userId" and "name" = 'Glacier AI';;
            `);
    }
    async down(queryRunner) { }
}
exports.SchemaUpdate1730220731948 = SchemaUpdate1730220731948;
//# sourceMappingURL=1730220731948-schema-update.js.map