"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1729685441899 = void 0;
class SchemaUpdate1729685441899 {
    constructor() {
        this.name = 'SchemaUpdate1729685441899';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD "active" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD "modifiedBy" uuid`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD "createdBy" uuid`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD CONSTRAINT "FK_10196ce6a73cda30b0ff2f1fe4a" FOREIGN KEY ("createdBy") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD CONSTRAINT "FK_863692eb2ba2706d98fef661edb" FOREIGN KEY ("modifiedBy") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP CONSTRAINT "FK_863692eb2ba2706d98fef661edb"`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP CONSTRAINT "FK_10196ce6a73cda30b0ff2f1fe4a"`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP COLUMN "modifiedBy"`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP COLUMN "createdBy"`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP COLUMN "active"`);
    }
}
exports.SchemaUpdate1729685441899 = SchemaUpdate1729685441899;
//# sourceMappingURL=1729685441899-schema-update.js.map