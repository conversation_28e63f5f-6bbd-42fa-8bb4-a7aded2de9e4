"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePromptManagementTables1700000000000 = void 0;
class CreatePromptManagementTables1700000000000 {
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TYPE "llm_model_enum" AS ENUM (
        'gpt-4o',
        'gpt-4o-mini',
        'o3-mini',
        'deepseek-r1',
        'o3',
        'o4-mini'
      )
    `);
        await queryRunner.query(`
      CREATE TABLE "llm_prompts" (
        "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
        "feature" varchar(100) NOT NULL,
        "chainIdentifier" varchar(10) NOT NULL,
        "prompt" text NOT NULL,
        "model" llm_model_enum NOT NULL,
        "requiredVariables" jsonb,
        "endpoint" varchar(255) NOT NULL,
        "isActive" boolean NOT NULL DEFAULT true,
        "description" text,
        "createdAt" timestamp NOT NULL DEFAULT now(),
        "updatedAt" timestamp NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_llm_prompts_feature_chain" UNIQUE ("feature", "chainIdentifier")
      )
    `);
        await queryRunner.query(`
      CREATE TABLE "llm_prompt_history" (
        "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
        "promptId" uuid NOT NULL,
        "oldPrompt" text NOT NULL,
        "newPrompt" text NOT NULL,
        "changes" jsonb,
        "changedById" uuid,
        "createdAt" timestamp NOT NULL DEFAULT now(),
        CONSTRAINT "FK_llm_prompt_history_promptId" FOREIGN KEY ("promptId") 
          REFERENCES "llm_prompts"("id") ON DELETE CASCADE,
        CONSTRAINT "FK_llm_prompt_history_changedById" FOREIGN KEY ("changedById") 
          REFERENCES "user"("id") ON DELETE SET NULL
      )
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "llm_prompt_history"`);
        await queryRunner.query(`DROP TABLE "llm_prompts"`);
        await queryRunner.query(`DROP TYPE "llm_model_enum"`);
    }
}
exports.CreatePromptManagementTables1700000000000 = CreatePromptManagementTables1700000000000;
//# sourceMappingURL=1749794404299-schema-update.js.map