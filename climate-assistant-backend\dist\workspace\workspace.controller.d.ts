import { WorkspaceService } from './workspace.service';
import { USER_ROLES } from 'src/constants';
import { DatapointDataRequestSharedService } from '../shared/shared-datapoint-datarequest.service';
import { VersionHistory } from './entities/version-history.entity';
import { Repository } from 'typeorm';
export declare class WorkspaceController {
    private readonly workspaceService;
    private readonly sharedService;
    private readonly versionHistoryRepository;
    constructor(workspaceService: WorkspaceService, sharedService: DatapointDataRequestSharedService, versionHistoryRepository: Repository<VersionHistory>);
    getWorkspaceDetails(req: any): Promise<import("./entities/workspace.entity").Workspace>;
    getUsersByWorkspace(req: any): Promise<import("../users/entities/user.entity").User[]>;
    getAllWorkspaces(): Promise<import("./entities/workspace.entity").Workspace[]>;
    addUserToWorkspace(req: any, body: {
        emails: string[];
        role: USER_ROLES;
    }): Promise<{
        success?: string;
        failure?: string;
    }>;
    getVersionHistory(req: any, refId: string): Promise<VersionHistory[]>;
    restoreVersion(req: any, body: {
        versionId: string;
    }): Promise<import("../data-request/entities/data-request.entity").DataRequest | import("../datapoint/entities/datapoint-request.entity").DatapointRequest>;
    getWorkspaceById(workspaceId: string): Promise<import("./entities/workspace.entity").Workspace>;
    updateWorkspaceById(req: any, body: any): Promise<import("./entities/workspace.entity").Workspace>;
    updateCompanyDetail(req: any, body: any): Promise<import("./entities/company.entity").Company>;
}
