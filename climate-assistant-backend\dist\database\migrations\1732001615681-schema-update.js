"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1732001615681 = void 0;
class SchemaUpdate1732001615681 {
    constructor() {
        this.name = 'SchemaUpdate1732001615681';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."esrs_topic_level_enum" AS ENUM('topic', 'sub-topic', 'sub-sub-topic')`);
        await queryRunner.query(`ALTER TABLE "esrs_topic" ADD "level" "public"."esrs_topic_level_enum" NOT NULL DEFAULT 'topic'`);
        await queryRunner.query(`ALTER TABLE "esrs_topic" ADD "parentId" integer`);
        await queryRunner.query(`ALTER TABLE "esrs_topic" ADD CONSTRAINT "FK_eeffc3f8ec9b2cc7f62baae3d48" FOREIGN KEY ("parentId") REFERENCES "esrs_topic"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (12, 'Climate change adaptation', 'sub-topic', 1);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (13, 'Climate change mitigation', 'sub-topic', 1);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (14, 'Energy', 'sub-topic', 1);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (15, 'Pollution of air', 'sub-topic', 2);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (16, 'Pollution of water', 'sub-topic', 2);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (17, 'Pollution of soil', 'sub-topic', 2);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (18, 'Pollution of living organisms and food resources', 'sub-topic', 2);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (19, 'Substances of concern', 'sub-topic', 2);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (20, 'Substances of very high concern', 'sub-topic', 2);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (21, 'Microplastics', 'sub-topic', 2);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (22, 'Water', 'sub-topic', 3);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (23, 'Marine resources', 'sub-topic', 3);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (24, 'Direct impact drivers of biodiversity loss', 'sub-topic', 4);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (25, 'Impacts on the state of species', 'sub-topic', 4);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (26, 'Impacts on the extent and condition of ecosystems', 'sub-topic', 4);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (27, 'Impacts and dependencies on ecosystem services', 'sub-topic', 4);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (28, 'Resources inflows, including resource use', 'sub-topic', 5);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (29, 'Resource outflows related to products and services', 'sub-topic', 5);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (30, 'Waste', 'sub-topic', 5);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (31, 'Working conditions', 'sub-topic', 6);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (32, 'Equal treatment and opportunities for all', 'sub-topic', 6);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (33, 'Other work-related rights', 'sub-topic', 6);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (34, 'Communities economic, social and cultural rights', 'sub-topic', 8);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (35, 'Communities civil and political rights', 'sub-topic', 8);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (36, 'Rights of indigenous peoples', 'sub-topic', 8);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (37, 'Information-related impacts for consumers and/or end-users', 'sub-topic', 9);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (38, 'Personal safety of consumers and/or end-users', 'sub-topic', 9);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (39, 'Social inclusion of consumers and/or end-users', 'sub-topic', 9);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (40, 'Corporate culture', 'sub-topic', 10);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (41, 'Protection of whistle-blowers', 'sub-topic', 10);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (42, 'Animal welfare', 'sub-topic', 10);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (43, 'Political engagement and lobbying activities', 'sub-topic', 10);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (44, 'Management of relationships with suppliers including payment practices', 'sub-topic', 10);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (45, 'Corruption and bribery', 'sub-topic', 10);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (46, 'Water consumption', 'sub-sub-topic', 22);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (47, 'Water withdrawals', 'sub-sub-topic', 22);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (48, 'Water discharges', 'sub-sub-topic', 22);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (49, 'Water discharges in the oceans', 'sub-sub-topic', 22);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (50, 'Extraction and use of marine resources', 'sub-sub-topic', 22);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (51, 'Water consumption', 'sub-sub-topic', 23);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (52, 'Water withdrawals', 'sub-sub-topic', 23);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (53, 'Water discharges', 'sub-sub-topic', 23);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (54, 'Water discharges in the oceans', 'sub-sub-topic', 23);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (55, 'Extraction and use of marine resources', 'sub-sub-topic', 23);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (56, 'Climate Change', 'sub-sub-topic', 24);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (57, 'Land-use change, fresh water-use change and sea-use change', 'sub-sub-topic', 24);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (58, 'Direct exploitation', 'sub-sub-topic', 24);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (59, 'Invasive alien species', 'sub-sub-topic', 24);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (60, 'Pollution', 'sub-sub-topic', 24);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (61, 'Others', 'sub-sub-topic', 24);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (62, 'Examples:', 'sub-sub-topic', 25);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (63, 'Species population size', 'sub-sub-topic', 25);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (64, 'Species global extinction risk', 'sub-sub-topic', 25);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (65, 'Examples:', 'sub-sub-topic', 26);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (66, 'and degradation', 'sub-sub-topic', 26);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (67, 'Desertification', 'sub-sub-topic', 26);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (68, 'Soil sealing', 'sub-sub-topic', 26);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (69, 'Secure employment', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (70, 'Working time', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (71, 'Adequate wages', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (72, 'Social dialogue', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (73, 'Freedom of association, the existence of works councils and the information. consultation and participation rights of workers', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (74, 'Collective bargaining, including rate of workers covered by collective agreements', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (75, 'Work-life balance', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (76, 'Health and safety', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (77, 'Gender equality and equal pay for work of equal value', 'sub-sub-topic', 32);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (78, 'Training and skills development', 'sub-sub-topic', 32);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (79, 'Employment and inclusion of persons with disabilities', 'sub-sub-topic', 32);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (80, 'Measures against violence and harassment in the workplace', 'sub-sub-topic', 32);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (81, 'Diversity', 'sub-sub-topic', 32);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (82, 'Child labour', 'sub-sub-topic', 33);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (83, 'Forced labour', 'sub-sub-topic', 33);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (84, 'Adequate housing', 'sub-sub-topic', 33);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (85, 'Privacy', 'sub-sub-topic', 33);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (86, 'Secure employment', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (87, 'Working time', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (88, 'Adequate wages', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (89, 'Social dialogue', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (90, 'Freedom of association, including the existence of work councils', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (91, 'Collective bargaining', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (92, 'Work-life balance', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (93, 'Health and safety', 'sub-sub-topic', 31);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (94, 'Gender equality and equal pay for work of equal value', 'sub-sub-topic', 32);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (95, 'Training and skills development', 'sub-sub-topic', 32);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (96, 'The employment and inclusion of persons with disabilities', 'sub-sub-topic', 32);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (97, 'Measures against violence and harassment in the workplace', 'sub-sub-topic', 32);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (98, 'Diversity', 'sub-sub-topic', 32);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (99, 'Child labour', 'sub-sub-topic', 33);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (100, 'Forced labour', 'sub-sub-topic', 33);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (101, 'Adequate housing', 'sub-sub-topic', 33);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (102, 'Water and sanitation', 'sub-sub-topic', 33);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (103, 'Privacy', 'sub-sub-topic', 33);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (104, 'Adequate housing', 'sub-sub-topic', 34);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (105, 'Adequate food', 'sub-sub-topic', 34);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (106, 'Water and sanitation', 'sub-sub-topic', 34);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (107, 'Land-related impacts', 'sub-sub-topic', 34);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (108, 'Security-related impacts', 'sub-sub-topic', 34);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (109, 'Freedom of expression', 'sub-sub-topic', 35);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (110, 'Freedom of assembly', 'sub-sub-topic', 35);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (111, 'Impacts on human rights defenders', 'sub-sub-topic', 35);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (112, 'Free, prior and informed consent', 'sub-sub-topic', 36);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (113, 'Self-determination', 'sub-sub-topic', 36);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (114, 'Cultural rights', 'sub-sub-topic', 36);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (115, 'Privacy', 'sub-sub-topic', 37);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (116, 'Freedom of expression', 'sub-sub-topic', 37);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (117, 'Access to (quality) information', 'sub-sub-topic', 37);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (118, 'Health and safety', 'sub-sub-topic', 38);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (119, 'Security of person', 'sub-sub-topic', 38);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (120, 'Protection of children', 'sub-sub-topic', 38);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (121, 'Non-discrimination', 'sub-sub-topic', 39);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (122, 'Access to products and services', 'sub-sub-topic', 39);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (123, 'Responsible marketing practices', 'sub-sub-topic', 39);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (124, 'Prevention and detection including training', 'sub-sub-topic', 45);
      INSERT INTO esrs_topic (id, name, level, "parentId") VALUES (125, 'Incidents', 'sub-sub-topic', 45);`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_topic" DROP CONSTRAINT "FK_eeffc3f8ec9b2cc7f62baae3d48"`);
        await queryRunner.query(`ALTER TABLE "esrs_topic" DROP COLUMN "parentId"`);
        await queryRunner.query(`ALTER TABLE "esrs_topic" DROP COLUMN "level"`);
        await queryRunner.query(`DROP TYPE "public"."esrs_topic_level_enum"`);
    }
}
exports.SchemaUpdate1732001615681 = SchemaUpdate1732001615681;
//# sourceMappingURL=1732001615681-schema-update.js.map