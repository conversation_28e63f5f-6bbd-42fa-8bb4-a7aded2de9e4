"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1729019561528 = void 0;
class SchemaUpdate1729019561528 {
    constructor() {
        this.name = 'SchemaUpdate1729019561528';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "data_request" ALTER COLUMN "content" SET DEFAULT ''`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "data_request" ALTER COLUMN "content" DROP DEFAULT`);
    }
}
exports.SchemaUpdate1729019561528 = SchemaUpdate1729019561528;
//# sourceMappingURL=1729019561528-schema-update.js.map