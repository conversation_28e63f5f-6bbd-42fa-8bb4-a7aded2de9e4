"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1729034378206 = void 0;
class SchemaUpdate1729034378206 {
    constructor() {
        this.name = 'SchemaUpdate1729034378206';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document_chunk" ADD "matchingsJson" text NOT NULL DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ADD "paragraph" character varying NOT NULL DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ADD "relatedAR" character varying NOT NULL DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ADD "lawText" text NOT NULL DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ADD "lawTextAR" text NOT NULL DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ADD "updatedAt" TIMESTAMP NOT NULL DEFAULT now()`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" DROP COLUMN "updatedAt"`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" DROP COLUMN "createdAt"`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" DROP COLUMN "lawTextAR"`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" DROP COLUMN "lawText"`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" DROP COLUMN "relatedAR"`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" DROP COLUMN "paragraph"`);
        await queryRunner.query(`ALTER TABLE "document_chunk" DROP COLUMN "matchingsJson"`);
    }
}
exports.SchemaUpdate1729034378206 = SchemaUpdate1729034378206;
//# sourceMappingURL=1729034378206-schema-update.js.map