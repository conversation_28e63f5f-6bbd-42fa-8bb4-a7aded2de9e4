import { ESRSTopicDisclosureRequirement } from './esrs_topic_disclosure_requirement.entity';
import { ESRSTopicDatapoint } from './esrs-topic-datapoint.entity';
import { MaterialESRSTopic } from '../../project/entities/material-esrs-topic.entity';
export declare enum ESRSTopicLevel {
    TOPIC = "topic",
    SUB_TOPIC = "sub-topic",
    SUB_SUB_TOPIC = "sub-sub-topic"
}
export declare class ESRSTopic {
    id: number;
    name: string;
    level: string;
    description?: string;
    parentId: number;
    parent: ESRSTopic | null;
    children: ESRSTopic[];
    disclosureRequirementRelations: ESRSTopicDisclosureRequirement[];
    datapointRelations: ESRSTopicDatapoint[];
    materialTopics: MaterialESRSTopic[];
}
