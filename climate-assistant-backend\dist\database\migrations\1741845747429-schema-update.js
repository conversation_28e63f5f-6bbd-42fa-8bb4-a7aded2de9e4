"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1741845747429 = void 0;
class SchemaUpdate1741845747429 {
    constructor() {
        this.name = 'SchemaUpdate1741845747429';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TYPE "public"."project_primarycontentlanguage_enum" RENAME TO "project_primarycontentlanguage_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."project_primarycontentlanguage_enum" AS ENUM('BG', 'HR', 'CS', 'DA', 'NL', 'EN', 'ET', 'FI', 'FR', 'DE', 'EL', 'HU', 'GA', 'IT', 'LV', 'LT', 'MT', 'PL', 'PT', 'RO', 'SK', 'SL', 'ES', 'SV')`);
        await queryRunner.query(`ALTER TABLE "project" ALTER COLUMN "primaryContentLanguage" TYPE "public"."project_primarycontentlanguage_enum" USING "primaryContentLanguage"::"text"::"public"."project_primarycontentlanguage_enum"`);
        await queryRunner.query(`DROP TYPE "public"."project_primarycontentlanguage_enum_old"`);
    }
    async down(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."project_primarycontentlanguage_enum_old" AS ENUM('DE', 'EN')`);
        await queryRunner.query(`ALTER TABLE "project" ALTER COLUMN "primaryContentLanguage" TYPE "public"."project_primarycontentlanguage_enum_old" USING "primaryContentLanguage"::"text"::"public"."project_primarycontentlanguage_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."project_primarycontentlanguage_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."project_primarycontentlanguage_enum_old" RENAME TO "project_primarycontentlanguage_enum"`);
    }
}
exports.SchemaUpdate1741845747429 = SchemaUpdate1741845747429;
//# sourceMappingURL=1741845747429-schema-update.js.map