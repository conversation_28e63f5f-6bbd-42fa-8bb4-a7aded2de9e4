"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1729755709640 = void 0;
class SchemaUpdate1729755709640 {
    constructor() {
        this.name = 'SchemaUpdate1729755709640';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d"`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP CONSTRAINT "FK_10196ce6a73cda30b0ff2f1fe4a"`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ALTER COLUMN "documentChunkId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ALTER COLUMN "createdBy" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d" FOREIGN KEY ("documentChunkId") REFERENCES "document_chunk"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD CONSTRAINT "FK_10196ce6a73cda30b0ff2f1fe4a" FOREIGN KEY ("createdBy") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP CONSTRAINT "FK_10196ce6a73cda30b0ff2f1fe4a"`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d"`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ALTER COLUMN "createdBy" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ALTER COLUMN "documentChunkId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD CONSTRAINT "FK_10196ce6a73cda30b0ff2f1fe4a" FOREIGN KEY ("createdBy") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" ADD CONSTRAINT "FK_2a5f6913cfb1a14844a6ae4652d" FOREIGN KEY ("documentChunkId") REFERENCES "document_chunk"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "datapoint_document_chunk" DROP COLUMN "createdAt"`);
    }
}
exports.SchemaUpdate1729755709640 = SchemaUpdate1729755709640;
//# sourceMappingURL=1729755709640-schema-update.js.map