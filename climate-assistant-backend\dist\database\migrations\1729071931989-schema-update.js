"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1729071931989 = void 0;
class SchemaUpdate1729071931989 {
    constructor() {
        this.name = 'SchemaUpdate1729071931989';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document_chunk" ALTER COLUMN "matchingsJson" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ALTER COLUMN "paragraph" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ALTER COLUMN "relatedAR" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ALTER COLUMN "lawText" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ALTER COLUMN "lawTextAR" DROP DEFAULT`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ALTER COLUMN "lawTextAR" SET DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ALTER COLUMN "lawText" SET DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ALTER COLUMN "relatedAR" SET DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "esrs_datapoint" ALTER COLUMN "paragraph" SET DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "document_chunk" ALTER COLUMN "matchingsJson" SET DEFAULT ''`);
    }
}
exports.SchemaUpdate1729071931989 = SchemaUpdate1729071931989;
//# sourceMappingURL=1729071931989-schema-update.js.map