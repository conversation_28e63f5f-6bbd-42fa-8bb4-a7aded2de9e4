"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1742447371560 = void 0;
class SchemaUpdate1742447371560 {
    constructor() {
        this.name = 'SchemaUpdate1742447371560';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "project" ADD "reportingYear" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "reportingYear"`);
    }
}
exports.SchemaUpdate1742447371560 = SchemaUpdate1742447371560;
//# sourceMappingURL=1742447371560-schema-update.js.map