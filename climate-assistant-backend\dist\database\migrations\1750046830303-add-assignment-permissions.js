"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddAssignmentPermissions1750046830303 = void 0;
const constants_1 = require("../../constants");
class AddAssignmentPermissions1750046830303 {
    constructor() {
        this.name = 'AddAssignmentPermissions1750046830303';
    }
    async up(queryRunner) {
        await queryRunner.query(`
      INSERT INTO "permission" ("name", "description")
      VALUES ($1, $2)
      ON CONFLICT ("name") DO NOTHING
      `, [constants_1.SystemPermissions.ASSIGN_USERS_DATAPOINTS, 'Assign users to datapoints']);
        await queryRunner.query(`
      WITH role_data AS (
        SELECT id FROM "role" WHERE name IN ('WORKSPACE_ADMIN', 'SUPER_ADMIN')
      ),
      permission_data AS (
        SELECT id FROM "permission" WHERE name = $1
      )
      INSERT INTO "role_permission" ("roleId", "permissionId")
      SELECT rd.id, pd.id
      FROM role_data rd
      CROSS JOIN permission_data pd
      ON CONFLICT ("roleId", "permissionId") DO NOTHING
      `, [constants_1.SystemPermissions.ASSIGN_USERS_DATAPOINTS]);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      DELETE FROM "role_permission"
      WHERE "roleId" IN (SELECT id FROM "role" WHERE name IN ('WORKSPACE_ADMIN', 'SUPER_ADMIN'))
      AND "permissionId" IN (SELECT id FROM "permission" WHERE name = $1)
      `, [constants_1.SystemPermissions.ASSIGN_USERS_DATAPOINTS]);
        await queryRunner.query(`
      DELETE FROM "permission"
      WHERE name = $1
      AND NOT EXISTS (
        SELECT 1 FROM "role_permission" rp
        WHERE rp."permissionId" = "permission".id
      )
      `, [constants_1.SystemPermissions.ASSIGN_USERS_DATAPOINTS]);
    }
}
exports.AddAssignmentPermissions1750046830303 = AddAssignmentPermissions1750046830303;
//# sourceMappingURL=1750046830303-add-assignment-permissions.js.map