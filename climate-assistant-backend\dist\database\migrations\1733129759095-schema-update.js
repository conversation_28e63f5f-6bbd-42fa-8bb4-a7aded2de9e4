"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1733129759095 = void 0;
class SchemaUpdate1733129759095 {
    async up(queryRunner) {
        await queryRunner.query(`
            UPDATE "document"
            SET 
              "year" = 2024,
              "esrsCategory" = ARRAY['ESRS 2', 'ESRS S', 'ESRS G', 'ESRS E']::text[],
              "documentType" = 'Other'
            WHERE "year" IS NULL OR "esrsCategory" IS NULL OR "documentType" IS NULL
          `);
    }
    async down() { }
}
exports.SchemaUpdate1733129759095 = SchemaUpdate1733129759095;
//# sourceMappingURL=1733129759095-schema-update.js.map