"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1728899767133 = void 0;
class SchemaUpdate1728899767133 {
    constructor() {
        this.name = 'SchemaUpdate1728899767133';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "token" ADD "id" SERIAL NOT NULL`);
        await queryRunner.query(`ALTER TABLE "token" DROP CONSTRAINT "PK_94f168faad896c0786646fa3d4a"`);
        await queryRunner.query(`ALTER TABLE "token" ADD CONSTRAINT "PK_051ecc437e188cac3d16a1ae4d3" PRIMARY KEY ("userId", "id")`);
        await queryRunner.query(`ALTER TABLE "token" DROP CONSTRAINT "FK_94f168faad896c0786646fa3d4a"`);
        await queryRunner.query(`ALTER TABLE "token" DROP CONSTRAINT "PK_051ecc437e188cac3d16a1ae4d3"`);
        await queryRunner.query(`ALTER TABLE "token" ADD CONSTRAINT "PK_82fae97f905930df5d62a702fc9" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "password" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "token" ADD CONSTRAINT "FK_94f168faad896c0786646fa3d4a" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "token" DROP CONSTRAINT "FK_94f168faad896c0786646fa3d4a"`);
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "password" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "token" DROP CONSTRAINT "PK_82fae97f905930df5d62a702fc9"`);
        await queryRunner.query(`ALTER TABLE "token" ADD CONSTRAINT "PK_051ecc437e188cac3d16a1ae4d3" PRIMARY KEY ("userId", "id")`);
        await queryRunner.query(`ALTER TABLE "token" ADD CONSTRAINT "FK_94f168faad896c0786646fa3d4a" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "token" DROP CONSTRAINT "PK_051ecc437e188cac3d16a1ae4d3"`);
        await queryRunner.query(`ALTER TABLE "token" ADD CONSTRAINT "PK_94f168faad896c0786646fa3d4a" PRIMARY KEY ("userId")`);
        await queryRunner.query(`ALTER TABLE "token" DROP COLUMN "id"`);
    }
}
exports.SchemaUpdate1728899767133 = SchemaUpdate1728899767133;
//# sourceMappingURL=1728899767133-schema-update.js.map