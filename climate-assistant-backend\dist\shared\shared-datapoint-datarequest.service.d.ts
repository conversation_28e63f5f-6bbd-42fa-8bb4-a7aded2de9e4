import { Repository } from 'typeorm';
import { DatapointRequestData } from 'src/data-request/entities/data-request.dto';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { Queue } from 'bull';
import { DataRequest } from 'src/data-request/entities/data-request.entity';
export declare class DatapointDataRequestSharedService {
    private readonly datapointRequestRepository;
    private readonly dataRequestRepository;
    private readonly datapointGenerationQueue;
    private readonly datapointReviewQueue;
    constructor(datapointRequestRepository: Repository<DatapointRequest>, dataRequestRepository: Repository<DataRequest>, datapointGenerationQueue: Queue, datapointReviewQueue: Queue);
    private readonly logger;
    addDatapointToGenerationQueue({ datapointRequest, userId, workspaceId, useExistingReportTextForReference, }: {
        datapointRequest: DatapointRequestData;
        userId: string;
        workspaceId: string;
        useExistingReportTextForReference: boolean;
    }): Promise<void>;
    addDatapointToReviewQueue({ datapointRequest, userId, workspaceId, }: {
        datapointRequest: DatapointRequestData;
        userId: string;
        workspaceId: string;
    }): Promise<void>;
    restoreVersion({ versionId, requestId, content, event }: {
        versionId: any;
        requestId: any;
        content: any;
        event: any;
    }): Promise<DataRequest | DatapointRequest>;
}
