"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1731514409055 = void 0;
class SchemaUpdate1731514409055 {
    constructor() {
        this.name = 'SchemaUpdate1731514409055';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "project" ADD "reportTextGenerationRules" text NOT NULL DEFAULT ''`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "reportTextGenerationRules"`);
    }
}
exports.SchemaUpdate1731514409055 = SchemaUpdate1731514409055;
//# sourceMappingURL=1731514409055-schema-update.js.map