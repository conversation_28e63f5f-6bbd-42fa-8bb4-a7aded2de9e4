{"version": 3, "file": "1750046830302-schema-update.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/1750046830302-schema-update.ts"], "names": [], "mappings": ";;;AACA,+CAAoD;AAGpD,MAAM,eAAe,GAAG;IACtB,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,6BAAiB,CAAC;IAC7C,eAAe,EAAE;QACf,6BAAiB,CAAC,0BAA0B;QAC5C,6BAAiB,CAAC,0BAA0B;QAC5C,6BAAiB,CAAC,iBAAiB;QACnC,6BAAiB,CAAC,eAAe;QACjC,6BAAiB,CAAC,kBAAkB;QACpC,6BAAiB,CAAC,eAAe;QACjC,6BAAiB,CAAC,uBAAuB;QACzC,6BAAiB,CAAC,uBAAuB;QACzC,6BAAiB,CAAC,UAAU;QAC5B,6BAAiB,CAAC,QAAQ;QAC1B,6BAAiB,CAAC,gBAAgB;QAClC,6BAAiB,CAAC,WAAW;QAC7B,6BAAiB,CAAC,QAAQ;QAC1B,6BAAiB,CAAC,sBAAsB;QACxC,6BAAiB,CAAC,uBAAuB;QACzC,6BAAiB,CAAC,eAAe;QACjC,6BAAiB,CAAC,aAAa;QAC/B,6BAAiB,CAAC,uBAAuB;QACzC,6BAAiB,CAAC,qBAAqB;QACvC,6BAAiB,CAAC,YAAY;QAC9B,6BAAiB,CAAC,iBAAiB;QACnC,6BAAiB,CAAC,gBAAgB;QAClC,6BAAiB,CAAC,iBAAiB;QACnC,6BAAiB,CAAC,mBAAmB;QACrC,6BAAiB,CAAC,eAAe;QACjC,6BAAiB,CAAC,uBAAuB;KAC1C;IACD,cAAc,EAAE;QACd,6BAAiB,CAAC,0BAA0B;QAC5C,6BAAiB,CAAC,iBAAiB;QACnC,6BAAiB,CAAC,eAAe;QACjC,6BAAiB,CAAC,kBAAkB;QACpC,6BAAiB,CAAC,eAAe;QACjC,6BAAiB,CAAC,uBAAuB;QACzC,6BAAiB,CAAC,uBAAuB;QACzC,6BAAiB,CAAC,UAAU;QAC5B,6BAAiB,CAAC,QAAQ;QAC1B,6BAAiB,CAAC,gBAAgB;QAClC,6BAAiB,CAAC,WAAW;QAC7B,6BAAiB,CAAC,QAAQ;QAC1B,6BAAiB,CAAC,sBAAsB;QACxC,6BAAiB,CAAC,uBAAuB;QACzC,6BAAiB,CAAC,eAAe;QACjC,6BAAiB,CAAC,aAAa;QAC/B,6BAAiB,CAAC,iBAAiB;QACnC,6BAAiB,CAAC,mBAAmB;QACrC,6BAAiB,CAAC,eAAe;QACjC,6BAAiB,CAAC,uBAAuB;KAC1C;IACD,WAAW,EAAE;QACX,6BAAiB,CAAC,0BAA0B;QAC5C,6BAAiB,CAAC,eAAe;QACjC,6BAAiB,CAAC,iBAAiB;KACpC;CACF,CAAC;AAEF,MAAa,yBAAyB;IAAtC;QACE,SAAI,GAAG,2BAA2B,CAAC;IAqHrC,CAAC;IAnHC,wBAAwB,CAAC,UAA6B;QACpD,MAAM,YAAY,GAA2B;YAE3C,CAAC,6BAAiB,CAAC,iBAAiB,CAAC,EAAE,uBAAuB;YAC9D,CAAC,6BAAiB,CAAC,eAAe,CAAC,EAAE,0BAA0B;YAC/D,CAAC,6BAAiB,CAAC,kBAAkB,CAAC,EAAE,oBAAoB;YAC5D,CAAC,6BAAiB,CAAC,eAAe,CAAC,EAAE,6BAA6B;YAClE,CAAC,6BAAiB,CAAC,uBAAuB,CAAC,EAAE,4BAA4B;YAGzE,CAAC,6BAAiB,CAAC,UAAU,CAAC,EAAE,gBAAgB;YAChD,CAAC,6BAAiB,CAAC,QAAQ,CAAC,EAAE,mBAAmB;YACjD,CAAC,6BAAiB,CAAC,WAAW,CAAC,EAAE,aAAa;YAC9C,CAAC,6BAAiB,CAAC,QAAQ,CAAC,EAAE,sBAAsB;YACpD,CAAC,6BAAiB,CAAC,gBAAgB,CAAC,EAAE,qBAAqB;YAC3D,CAAC,6BAAiB,CAAC,eAAe,CAAC,EAAE,qBAAqB;YAG1D,CAAC,6BAAiB,CAAC,uBAAuB,CAAC,EACzC,oCAAoC;YACtC,CAAC,6BAAiB,CAAC,8BAA8B,CAAC,EAChD,+BAA+B;YACjC,CAAC,6BAAiB,CAAC,gBAAgB,CAAC,EAAE,6BAA6B;YACnE,CAAC,6BAAiB,CAAC,uBAAuB,CAAC,EAAE,wBAAwB;YAGrE,CAAC,6BAAiB,CAAC,eAAe,CAAC,EAAE,iBAAiB;YACtD,CAAC,6BAAiB,CAAC,aAAa,CAAC,EAAE,eAAe;YAGlD,CAAC,6BAAiB,CAAC,gBAAgB,CAAC,EAAE,uBAAuB;YAC7D,CAAC,6BAAiB,CAAC,gBAAgB,CAAC,EAAE,2BAA2B;YACjE,CAAC,6BAAiB,CAAC,uBAAuB,CAAC,EAAE,yBAAyB;YACtE,CAAC,6BAAiB,CAAC,qBAAqB,CAAC,EAAE,uBAAuB;YAClE,CAAC,6BAAiB,CAAC,iBAAiB,CAAC,EAAE,mBAAmB;YAC1D,CAAC,6BAAiB,CAAC,YAAY,CAAC,EAAE,kBAAkB;YAGpD,CAAC,6BAAiB,CAAC,iBAAiB,CAAC,EAAE,mBAAmB;YAC1D,CAAC,6BAAiB,CAAC,mBAAmB,CAAC,EAAE,sBAAsB;YAG/D,CAAC,6BAAiB,CAAC,0BAA0B,CAAC,EAC5C,6BAA6B;YAC/B,CAAC,6BAAiB,CAAC,0BAA0B,CAAC,EAC5C,2BAA2B;YAG7B,CAAC,6BAAiB,CAAC,sBAAsB,CAAC,EAAE,sBAAsB;YAClE,CAAC,6BAAiB,CAAC,uBAAuB,CAAC,EACzC,iCAAiC;SACpC,CAAC;QAEF,OAAO,YAAY,CAAC,UAAU,CAAC,IAAI,cAAc,UAAU,EAAE,CAAC;IAChE,CAAC;IAEM,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,6BAAiB,CAAC;aAChD,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK,GAAG,CAAC;aAC5B,IAAI,CAAC,IAAI,CAAC,CAAC;QACd,MAAM,WAAW,CAAC,KAAK,CACrB,wDAAwD,UAAU,GAAG,CACtE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0RAA0R,CAC3R,CAAC;QACF,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,MAAM,CAAC,6BAAiB,CAAC,EAAE,CAAC;YAC1D,MAAM,WAAW,CAAC,KAAK,CACrB;;;;OAID,EACC,CAAC,UAAU,EAAE,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC,CACxD,CAAC;QACJ,CAAC;QACD,MAAM,WAAW,CAAC,KAAK,CACrB,2KAA2K,CAC5K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oKAAoK,CACrK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,gLAAgL,CACjL,CAAC;QACF,KAAK,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;YACtE,MAAM,WAAW,CAAC,KAAK,CACrB;;;;;;;;;;;OAWD,EACC,CAAC,QAAQ,EAAE,WAAW,CAAC,CACxB,CAAC;QACJ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,gFAAgF,CACjF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,gFAAgF,CACjF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;QACxD,MAAM,WAAW,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QACnD,MAAM,WAAW,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;IACvE,CAAC;CACF;AAtHD,8DAsHC"}