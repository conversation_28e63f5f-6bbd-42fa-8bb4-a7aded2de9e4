"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1750934407518 = void 0;
class SchemaUpdate1750934407518 {
    constructor() {
        this.name = 'SchemaUpdate1750934407518';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_request" ADD "content_version" uuid`);
        await queryRunner.query(`ALTER TABLE "data_request" ADD "content_version" uuid`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "data_request" DROP COLUMN "content_version"`);
        await queryRunner.query(`ALTER TABLE "datapoint_request" DROP COLUMN "content_version"`);
    }
}
exports.SchemaUpdate1750934407518 = SchemaUpdate1750934407518;
//# sourceMappingURL=1750934407518-schema-update.js.map