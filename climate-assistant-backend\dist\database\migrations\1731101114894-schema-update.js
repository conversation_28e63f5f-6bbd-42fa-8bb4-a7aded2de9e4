"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaUpdate1731101114894 = void 0;
class SchemaUpdate1731101114894 {
    constructor() {
        this.name = 'SchemaUpdate1731101114894';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document_chunk" ADD "metadataJson" json`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document_chunk" DROP COLUMN "metadataJson"`);
    }
}
exports.SchemaUpdate1731101114894 = SchemaUpdate1731101114894;
//# sourceMappingURL=1731101114894-schema-update.js.map